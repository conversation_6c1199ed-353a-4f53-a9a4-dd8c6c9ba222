/**
 * Products Sitemap Route Handler
 * 
 * Generates dynamic sitemap for all products to improve
 * search engine crawling and indexing of product pages.
 * 
 * <AUTHOR> Team
 */

import { NextRequest, NextResponse } from 'next/server'
import { collection, query, where, orderBy, limit, getDocs } from 'firebase/firestore'
import { db } from '@/lib/firebase'

interface Product {
  id: string
  name: string
  slug?: string
  isActive: boolean
  isPublic: boolean
  createdAt: any
  updatedAt?: any
  category?: string
  priority?: number
}

export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'
  
  try {
    const sitemap: string[] = []
    
    // XML header
    sitemap.push('<?xml version="1.0" encoding="UTF-8"?>')
    sitemap.push('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">')
    
    if (db) {
      // Get active public products
      const productsQuery = query(
        collection(db, 'products'),
        where('isActive', '==', true),
        where('isPublic', '==', true),
        orderBy('createdAt', 'desc'),
        limit(5000) // Limit to prevent oversized sitemaps
      )
      
      const productsSnapshot = await getDocs(productsQuery)
      
      productsSnapshot.docs.forEach(doc => {
        const product = doc.data() as Product
        const slug = product.slug || generateSlug(product.name)
        const lastModified = product.updatedAt?.toDate() || product.createdAt?.toDate() || new Date()
        const priority = calculateProductPriority(product)
        const changeFreq = getProductChangeFrequency(product)
        
        sitemap.push('  <url>')
        sitemap.push(`    <loc>${baseUrl}/products/${doc.id}/${slug}</loc>`)
        sitemap.push(`    <lastmod>${lastModified.toISOString()}</lastmod>`)
        sitemap.push(`    <changefreq>${changeFreq}</changefreq>`)
        sitemap.push(`    <priority>${priority.toFixed(1)}</priority>`)
        sitemap.push('  </url>')
        
        // Also add shop URLs for products
        sitemap.push('  <url>')
        sitemap.push(`    <loc>${baseUrl}/shop/${doc.id}</loc>`)
        sitemap.push(`    <lastmod>${lastModified.toISOString()}</lastmod>`)
        sitemap.push(`    <changefreq>${changeFreq}</changefreq>`)
        sitemap.push(`    <priority>${priority.toFixed(1)}</priority>`)
        sitemap.push('  </url>')
      })
    }
    
    // Close XML
    sitemap.push('</urlset>')
    
    const xmlContent = sitemap.join('\n')
    
    return new NextResponse(xmlContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    })
    
  } catch (error) {
    console.error('Error generating products sitemap:', error)
    
    // Return minimal sitemap on error
    const fallbackSitemap = [
      '<?xml version="1.0" encoding="UTF-8"?>',
      '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">',
      '  <url>',
      `    <loc>${baseUrl}/products</loc>`,
      `    <lastmod>${new Date().toISOString()}</lastmod>`,
      '    <changefreq>daily</changefreq>',
      '    <priority>0.9</priority>',
      '  </url>',
      '  <url>',
      `    <loc>${baseUrl}/shop</loc>`,
      `    <lastmod>${new Date().toISOString()}</lastmod>`,
      '    <changefreq>daily</changefreq>',
      '    <priority>0.9</priority>',
      '  </url>',
      '</urlset>'
    ].join('\n')
    
    return new NextResponse(fallbackSitemap, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=300, s-maxage=300', // Shorter cache on error
      },
    })
  }
}

/**
 * Generate URL-friendly slug from product name
 */
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
    .substring(0, 50)
}

/**
 * Calculate product priority based on various factors
 */
function calculateProductPriority(product: Product): number {
  let priority = 0.7 // Base priority for products
  
  // Boost priority for featured or popular products
  if (product.priority && product.priority > 0) {
    priority = Math.min(0.9, priority + (product.priority * 0.1))
  }
  
  // Boost priority for certain categories
  if (product.category) {
    const highPriorityCategories = ['keycaps', 'artisan', 'limited-edition']
    if (highPriorityCategories.includes(product.category.toLowerCase())) {
      priority = Math.min(0.9, priority + 0.1)
    }
  }
  
  return priority
}

/**
 * Determine change frequency based on product type
 */
function getProductChangeFrequency(product: Product): string {
  if (product.category) {
    const category = product.category.toLowerCase()
    
    // Limited edition and raffle items change more frequently
    if (category.includes('raffle') || category.includes('limited')) {
      return 'daily'
    }
    
    // Regular products change weekly
    if (category.includes('keycap') || category.includes('artisan')) {
      return 'weekly'
    }
  }
  
  return 'monthly'
}
