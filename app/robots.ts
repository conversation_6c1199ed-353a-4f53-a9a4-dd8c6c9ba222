/**
 * Robots.txt Route Handler
 * 
 * Generates dynamic robots.txt file for search engine crawlers
 * with proper directives for SEO optimization.
 * 
 * <AUTHOR> Team
 */

import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'
  
  return {
    rules: [
      // Allow all crawlers to access most content
      {
        userAgent: '*',
        allow: [
          '/',
          '/shop',
          '/shop/*',
          '/products',
          '/products/*',
          '/community',
          '/community/*',
          '/profile',
          '/blog',
          '/blog/*',
          '/about',
          '/contact',
          '/faq',
          '/support',
          '/terms-of-service',
          '/privacy-policy',
          '/shipping-returns'
        ],
        disallow: [
          // Admin routes
          '/admin',
          '/admin/*',
          
          // API routes
          '/api/*',
          
          // Private user areas
          '/profile/account',
          '/profile/orders',
          '/profile/settings',
          '/profile/privacy',
          '/profile/security',
          '/profile/billing',
          '/profile/payments',
          '/profile/addresses',
          '/profile/notifications',
          '/profile/downloads',
          '/profile/export',
          
          // Authentication pages
          '/auth',
          '/auth/*',
          '/register',
          '/login',
          
          // Cart and checkout
          '/cart',
          '/checkout',
          '/raffle-entry',
          
          // Test and development pages
          '/test',
          '/test/*',
          '/debug',
          '/debug/*',
          
          // Temporary or utility pages
          '/offline',
          '/sentry-example-page',
          
          // Search parameters and filters
          '/*?*sort=*',
          '/*?*filter=*',
          '/*?*page=*',
          
          // Duplicate content prevention
          '/*?utm_*',
          '/*?ref=*',
          '/*?source=*',
          '/*?campaign=*'
        ],
        crawlDelay: 1
      },
      
      // Special rules for Google
      {
        userAgent: 'Googlebot',
        allow: [
          '/',
          '/shop',
          '/shop/*',
          '/products',
          '/products/*',
          '/community/discussions/*',
          '/community/challenges/*',
          '/community/submissions/*',
          '/blog',
          '/blog/*'
        ],
        disallow: [
          '/admin/*',
          '/api/*',
          '/profile/account',
          '/profile/orders',
          '/auth/*',
          '/cart',
          '/checkout'
        ]
      },
      
      // Special rules for Bing
      {
        userAgent: 'Bingbot',
        allow: [
          '/',
          '/shop',
          '/products',
          '/community',
          '/blog'
        ],
        disallow: [
          '/admin/*',
          '/api/*',
          '/profile/*',
          '/auth/*'
        ],
        crawlDelay: 2
      },
      
      // Block aggressive crawlers
      {
        userAgent: [
          'AhrefsBot',
          'SemrushBot',
          'MJ12bot',
          'DotBot',
          'BLEXBot'
        ],
        disallow: '/'
      }
    ],
    
    // Sitemap locations
    sitemap: [
      `${baseUrl}/sitemap.xml`,
      `${baseUrl}/sitemap-products.xml`,
      `${baseUrl}/sitemap-community.xml`,
      `${baseUrl}/sitemap-blog.xml`
    ],
    
    // Additional directives
    host: baseUrl
  }
}
