/**
 * SEO Analytics API Route
 * 
 * API endpoint for collecting and retrieving SEO performance data
 * and analytics for monitoring and optimization.
 * 
 * <AUTHOR> Team
 */

import { NextRequest, NextResponse } from 'next/server'
import { collection, addDoc, query, where, orderBy, limit, getDocs, Timestamp } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { SEOMetrics, SEOAuditResult } from '@/lib/seo/seoAnalytics'

interface SEOAnalyticsData extends Partial<SEOMetrics> {
  userAgent?: string
  referrer?: string
  sessionId?: string
  userId?: string
}

/**
 * POST - Store SEO analytics data
 */
export async function POST(request: NextRequest) {
  try {
    const data: SEOAnalyticsData = await request.json()
    
    // Validate required fields
    if (!data.pageUrl) {
      return NextResponse.json(
        { error: 'pageUrl is required' },
        { status: 400 }
      )
    }
    
    // Enrich data with request information
    const enrichedData = {
      ...data,
      userAgent: request.headers.get('user-agent') || '',
      referrer: request.headers.get('referer') || '',
      timestamp: Timestamp.now(),
      ip: request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    }
    
    // Store in Firebase
    if (db) {
      await addDoc(collection(db, 'seo_analytics'), enrichedData)
    }
    
    return NextResponse.json({
      success: true,
      message: 'SEO analytics data stored successfully'
    })
    
  } catch (error) {
    console.error('Error storing SEO analytics:', error)
    return NextResponse.json(
      { error: 'Failed to store SEO analytics data' },
      { status: 500 }
    )
  }
}

/**
 * GET - Retrieve SEO analytics data
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action') || 'metrics'
    const url = searchParams.get('url')
    const days = parseInt(searchParams.get('days') || '30')
    const limitParam = parseInt(searchParams.get('limit') || '100')
    
    if (!db) {
      return NextResponse.json(
        { error: 'Database not available' },
        { status: 503 }
      )
    }
    
    switch (action) {
      case 'metrics': {
        // Get metrics for specific URL or all URLs
        let metricsQuery = query(
          collection(db, 'seo_analytics'),
          orderBy('timestamp', 'desc'),
          limit(limitParam)
        )
        
        if (url) {
          metricsQuery = query(
            collection(db, 'seo_analytics'),
            where('pageUrl', '==', url),
            orderBy('timestamp', 'desc'),
            limit(limitParam)
          )
        }
        
        const snapshot = await getDocs(metricsQuery)
        const metrics = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp?.toDate()
        }))
        
        return NextResponse.json({
          success: true,
          metrics,
          total: metrics.length
        })
      }
      
      case 'summary': {
        // Get summary statistics
        const summary = await generateSEOSummary(days)
        
        return NextResponse.json({
          success: true,
          summary,
          period: `${days} days`
        })
      }
      
      case 'issues': {
        // Get common SEO issues
        const issues = await getCommonSEOIssues(days)
        
        return NextResponse.json({
          success: true,
          issues,
          period: `${days} days`
        })
      }
      
      case 'performance': {
        // Get performance metrics
        const performance = await getPerformanceMetrics(url, days)
        
        return NextResponse.json({
          success: true,
          performance,
          url,
          period: `${days} days`
        })
      }
      
      case 'top-pages': {
        // Get top performing pages
        const topPages = await getTopPerformingPages(days, limitParam)
        
        return NextResponse.json({
          success: true,
          topPages,
          period: `${days} days`
        })
      }
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: metrics, summary, issues, performance, or top-pages' },
          { status: 400 }
        )
    }
    
  } catch (error) {
    console.error('Error retrieving SEO analytics:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve SEO analytics data' },
      { status: 500 }
    )
  }
}

/**
 * Generate SEO summary statistics
 */
async function generateSEOSummary(days: number) {
  if (!db) return null
  
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - days)
  
  try {
    const snapshot = await getDocs(
      query(
        collection(db, 'seo_analytics'),
        where('timestamp', '>=', Timestamp.fromDate(cutoffDate))
      )
    )
    
    const data = snapshot.docs.map(doc => doc.data())
    
    // Calculate summary metrics
    const totalPageViews = data.length
    const uniquePages = new Set(data.map(d => d.pageUrl)).size
    const avgLoadTime = data.reduce((sum, d) => sum + (d.loadTime || 0), 0) / data.length
    const avgWordCount = data.reduce((sum, d) => sum + (d.wordCount || 0), 0) / data.length
    
    // Performance distribution
    const performanceDistribution = {
      fast: data.filter(d => (d.loadTime || 0) < 2000).length,
      moderate: data.filter(d => (d.loadTime || 0) >= 2000 && (d.loadTime || 0) < 4000).length,
      slow: data.filter(d => (d.loadTime || 0) >= 4000).length
    }
    
    return {
      totalPageViews,
      uniquePages,
      avgLoadTime: Math.round(avgLoadTime),
      avgWordCount: Math.round(avgWordCount),
      performanceDistribution
    }
    
  } catch (error) {
    console.error('Error generating SEO summary:', error)
    return null
  }
}

/**
 * Get common SEO issues
 */
async function getCommonSEOIssues(days: number) {
  if (!db) return []
  
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - days)
  
  try {
    const snapshot = await getDocs(
      query(
        collection(db, 'seo_analytics'),
        where('timestamp', '>=', Timestamp.fromDate(cutoffDate))
      )
    )
    
    const data = snapshot.docs.map(doc => doc.data())
    const issues = []
    
    // Analyze common issues
    const pagesWithoutDescription = data.filter(d => !d.description || d.description.length === 0)
    if (pagesWithoutDescription.length > 0) {
      issues.push({
        type: 'Missing Meta Description',
        count: pagesWithoutDescription.length,
        percentage: Math.round((pagesWithoutDescription.length / data.length) * 100),
        severity: 'high'
      })
    }
    
    const shortContent = data.filter(d => (d.wordCount || 0) < 300)
    if (shortContent.length > 0) {
      issues.push({
        type: 'Short Content',
        count: shortContent.length,
        percentage: Math.round((shortContent.length / data.length) * 100),
        severity: 'medium'
      })
    }
    
    const slowPages = data.filter(d => (d.loadTime || 0) > 3000)
    if (slowPages.length > 0) {
      issues.push({
        type: 'Slow Load Time',
        count: slowPages.length,
        percentage: Math.round((slowPages.length / data.length) * 100),
        severity: 'high'
      })
    }
    
    return issues
    
  } catch (error) {
    console.error('Error getting SEO issues:', error)
    return []
  }
}

/**
 * Get performance metrics for a specific URL
 */
async function getPerformanceMetrics(url: string | null, days: number) {
  if (!db) return null
  
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - days)
  
  try {
    let metricsQuery = query(
      collection(db, 'seo_analytics'),
      where('timestamp', '>=', Timestamp.fromDate(cutoffDate)),
      orderBy('timestamp', 'desc')
    )
    
    if (url) {
      metricsQuery = query(
        collection(db, 'seo_analytics'),
        where('pageUrl', '==', url),
        where('timestamp', '>=', Timestamp.fromDate(cutoffDate)),
        orderBy('timestamp', 'desc')
      )
    }
    
    const snapshot = await getDocs(metricsQuery)
    const data = snapshot.docs.map(doc => doc.data())
    
    if (data.length === 0) return null
    
    // Calculate performance metrics
    const loadTimes = data.map(d => d.loadTime || 0).filter(t => t > 0)
    const avgLoadTime = loadTimes.reduce((sum, t) => sum + t, 0) / loadTimes.length
    const minLoadTime = Math.min(...loadTimes)
    const maxLoadTime = Math.max(...loadTimes)
    
    // Core Web Vitals averages
    const lcpValues = data.map(d => d.coreWebVitals?.lcp || 0).filter(v => v > 0)
    const fidValues = data.map(d => d.coreWebVitals?.fid || 0).filter(v => v > 0)
    const clsValues = data.map(d => d.coreWebVitals?.cls || 0).filter(v => v > 0)
    
    return {
      loadTime: {
        avg: Math.round(avgLoadTime),
        min: Math.round(minLoadTime),
        max: Math.round(maxLoadTime)
      },
      coreWebVitals: {
        lcp: lcpValues.length > 0 ? Math.round(lcpValues.reduce((sum, v) => sum + v, 0) / lcpValues.length) : 0,
        fid: fidValues.length > 0 ? Math.round(fidValues.reduce((sum, v) => sum + v, 0) / fidValues.length) : 0,
        cls: clsValues.length > 0 ? (clsValues.reduce((sum, v) => sum + v, 0) / clsValues.length).toFixed(3) : 0
      },
      sampleSize: data.length
    }
    
  } catch (error) {
    console.error('Error getting performance metrics:', error)
    return null
  }
}

/**
 * Get top performing pages
 */
async function getTopPerformingPages(days: number, limitParam: number) {
  if (!db) return []
  
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - days)
  
  try {
    const snapshot = await getDocs(
      query(
        collection(db, 'seo_analytics'),
        where('timestamp', '>=', Timestamp.fromDate(cutoffDate))
      )
    )
    
    const data = snapshot.docs.map(doc => doc.data())
    
    // Group by URL and calculate metrics
    const pageMetrics = data.reduce((acc, item) => {
      const url = item.pageUrl
      if (!acc[url]) {
        acc[url] = {
          url,
          title: item.title || '',
          views: 0,
          avgLoadTime: 0,
          avgWordCount: 0,
          loadTimes: []
        }
      }
      
      acc[url].views++
      if (item.loadTime) acc[url].loadTimes.push(item.loadTime)
      if (item.wordCount) acc[url].avgWordCount += item.wordCount
      
      return acc
    }, {} as Record<string, any>)
    
    // Calculate averages and sort
    const topPages = Object.values(pageMetrics)
      .map((page: any) => ({
        ...page,
        avgLoadTime: page.loadTimes.length > 0 
          ? Math.round(page.loadTimes.reduce((sum: number, t: number) => sum + t, 0) / page.loadTimes.length)
          : 0,
        avgWordCount: Math.round(page.avgWordCount / page.views)
      }))
      .sort((a: any, b: any) => b.views - a.views)
      .slice(0, limitParam)
    
    return topPages
    
  } catch (error) {
    console.error('Error getting top performing pages:', error)
    return []
  }
}
