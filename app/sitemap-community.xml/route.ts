/**
 * Community Sitemap Route Handler
 * 
 * Generates dynamic sitemap for community content including
 * discussions, challenges, submissions, and public profiles.
 * 
 * <AUTHOR> Team
 */

import { NextRequest, NextResponse } from 'next/server'
import { CommunitySitemapGenerator } from '@/lib/seo/sitemapGenerator'

export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'
  
  try {
    // Generate community sitemap using existing generator
    const sitemapEntries = await CommunitySitemapGenerator.generateCommunitySitemap()
    
    const sitemap: string[] = []
    
    // XML header
    sitemap.push('<?xml version="1.0" encoding="UTF-8"?>')
    sitemap.push('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">')
    
    // Add each sitemap entry
    sitemapEntries.forEach(entry => {
      sitemap.push('  <url>')
      sitemap.push(`    <loc>${entry.url}</loc>`)
      
      if (entry.lastModified) {
        sitemap.push(`    <lastmod>${entry.lastModified.toISOString()}</lastmod>`)
      }
      
      if (entry.changeFrequency) {
        sitemap.push(`    <changefreq>${entry.changeFrequency}</changefreq>`)
      }
      
      if (entry.priority) {
        sitemap.push(`    <priority>${entry.priority.toFixed(1)}</priority>`)
      }
      
      sitemap.push('  </url>')
    })
    
    // Close XML
    sitemap.push('</urlset>')
    
    const xmlContent = sitemap.join('\n')
    
    return new NextResponse(xmlContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=1800, s-maxage=1800', // Cache for 30 minutes
      },
    })
    
  } catch (error) {
    console.error('Error generating community sitemap:', error)
    
    // Return minimal community sitemap on error
    const fallbackSitemap = [
      '<?xml version="1.0" encoding="UTF-8"?>',
      '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">',
      '  <url>',
      `    <loc>${baseUrl}/community</loc>`,
      `    <lastmod>${new Date().toISOString()}</lastmod>`,
      '    <changefreq>daily</changefreq>',
      '    <priority>0.8</priority>',
      '  </url>',
      '  <url>',
      `    <loc>${baseUrl}/community/discussions</loc>`,
      `    <lastmod>${new Date().toISOString()}</lastmod>`,
      '    <changefreq>daily</changefreq>',
      '    <priority>0.7</priority>',
      '  </url>',
      '  <url>',
      `    <loc>${baseUrl}/community/challenges</loc>`,
      `    <lastmod>${new Date().toISOString()}</lastmod>`,
      '    <changefreq>weekly</changefreq>',
      '    <priority>0.7</priority>',
      '  </url>',
      '  <url>',
      `    <loc>${baseUrl}/community/submissions</loc>`,
      `    <lastmod>${new Date().toISOString()}</lastmod>`,
      '    <changefreq>daily</changefreq>',
      '    <priority>0.7</priority>',
      '  </url>',
      '  <url>',
      `    <loc>${baseUrl}/community/rankings</loc>`,
      `    <lastmod>${new Date().toISOString()}</lastmod>`,
      '    <changefreq>daily</changefreq>',
      '    <priority>0.6</priority>',
      '  </url>',
      '</urlset>'
    ].join('\n')
    
    return new NextResponse(fallbackSitemap, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=300, s-maxage=300', // Shorter cache on error
      },
    })
  }
}
