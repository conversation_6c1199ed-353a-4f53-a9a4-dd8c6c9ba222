/**
 * Blog Sitemap Route Handler
 * 
 * Generates dynamic sitemap for blog posts and articles
 * to improve search engine crawling and indexing.
 * 
 * <AUTHOR> Team
 */

import { NextRequest, NextResponse } from 'next/server'
import { collection, query, where, orderBy, limit, getDocs } from 'firebase/firestore'
import { db } from '@/lib/firebase'

interface BlogPost {
  id: string
  title: string
  slug?: string
  isPublished: boolean
  isPublic: boolean
  createdAt: any
  updatedAt?: any
  publishedAt?: any
  category?: string
  tags?: string[]
  author?: {
    name: string
    id: string
  }
}

export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'
  
  try {
    const sitemap: string[] = []
    
    // XML header
    sitemap.push('<?xml version="1.0" encoding="UTF-8"?>')
    sitemap.push('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">')
    
    if (db) {
      // Get published blog posts
      const blogQuery = query(
        collection(db, 'blog'),
        where('isPublished', '==', true),
        where('isPublic', '==', true),
        orderBy('publishedAt', 'desc'),
        limit(1000) // Limit to prevent oversized sitemaps
      )
      
      const blogSnapshot = await getDocs(blogQuery)
      
      blogSnapshot.docs.forEach(doc => {
        const post = doc.data() as BlogPost
        const slug = post.slug || generateSlug(post.title)
        const lastModified = post.updatedAt?.toDate() || post.publishedAt?.toDate() || post.createdAt?.toDate() || new Date()
        const priority = calculateBlogPriority(post)
        const changeFreq = getBlogChangeFrequency(post)
        
        sitemap.push('  <url>')
        sitemap.push(`    <loc>${baseUrl}/blog/${slug}</loc>`)
        sitemap.push(`    <lastmod>${lastModified.toISOString()}</lastmod>`)
        sitemap.push(`    <changefreq>${changeFreq}</changefreq>`)
        sitemap.push(`    <priority>${priority.toFixed(1)}</priority>`)
        sitemap.push('  </url>')
      })
    }
    
    // Close XML
    sitemap.push('</urlset>')
    
    const xmlContent = sitemap.join('\n')
    
    return new NextResponse(xmlContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600', // Cache for 1 hour
      },
    })
    
  } catch (error) {
    console.error('Error generating blog sitemap:', error)
    
    // Return minimal blog sitemap on error
    const fallbackSitemap = [
      '<?xml version="1.0" encoding="UTF-8"?>',
      '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">',
      '  <url>',
      `    <loc>${baseUrl}/blog</loc>`,
      `    <lastmod>${new Date().toISOString()}</lastmod>`,
      '    <changefreq>weekly</changefreq>',
      '    <priority>0.8</priority>',
      '  </url>',
      '</urlset>'
    ].join('\n')
    
    return new NextResponse(fallbackSitemap, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=300, s-maxage=300', // Shorter cache on error
      },
    })
  }
}

/**
 * Generate URL-friendly slug from blog title
 */
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
    .substring(0, 60)
}

/**
 * Calculate blog post priority based on various factors
 */
function calculateBlogPriority(post: BlogPost): number {
  let priority = 0.6 // Base priority for blog posts
  
  // Boost priority for recent posts
  const publishedDate = post.publishedAt?.toDate() || post.createdAt?.toDate()
  if (publishedDate) {
    const daysSincePublished = (Date.now() - publishedDate.getTime()) / (1000 * 60 * 60 * 24)
    if (daysSincePublished < 7) {
      priority = Math.min(0.8, priority + 0.2) // Recent posts get higher priority
    } else if (daysSincePublished < 30) {
      priority = Math.min(0.7, priority + 0.1)
    }
  }
  
  // Boost priority for certain categories
  if (post.category) {
    const highPriorityCategories = ['announcement', 'tutorial', 'guide', 'news']
    if (highPriorityCategories.includes(post.category.toLowerCase())) {
      priority = Math.min(0.8, priority + 0.1)
    }
  }
  
  // Boost priority for posts with popular tags
  if (post.tags && post.tags.length > 0) {
    const popularTags = ['keycaps', 'mechanical-keyboard', 'artisan', 'community']
    const hasPopularTag = post.tags.some(tag => 
      popularTags.includes(tag.toLowerCase())
    )
    if (hasPopularTag) {
      priority = Math.min(0.8, priority + 0.05)
    }
  }
  
  return priority
}

/**
 * Determine change frequency based on post age and type
 */
function getBlogChangeFrequency(post: BlogPost): string {
  const publishedDate = post.publishedAt?.toDate() || post.createdAt?.toDate()
  
  if (publishedDate) {
    const daysSincePublished = (Date.now() - publishedDate.getTime()) / (1000 * 60 * 60 * 24)
    
    // Recent posts might be updated more frequently
    if (daysSincePublished < 7) {
      return 'daily'
    } else if (daysSincePublished < 30) {
      return 'weekly'
    } else if (daysSincePublished < 90) {
      return 'monthly'
    }
  }
  
  // Older posts change less frequently
  return 'yearly'
}
