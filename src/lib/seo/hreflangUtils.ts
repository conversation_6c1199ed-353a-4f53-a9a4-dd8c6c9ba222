/**
 * Hreflang Utilities
 * 
 * Utilities for managing hreflang tags for international SEO
 * and multi-language support.
 * 
 * <AUTHOR> Team
 */

export interface HreflangEntry {
  hreflang: string
  href: string
  language: string
  region?: string
}

export interface LocalizedContent {
  language: string
  region?: string
  url: string
  title: string
  description: string
}

/**
 * Supported languages and regions for Syndicaps
 */
export const SUPPORTED_LOCALES = {
  'en': { language: 'English', region: 'Global' },
  'en-US': { language: 'English', region: 'United States' },
  'en-GB': { language: 'English', region: 'United Kingdom' },
  'en-CA': { language: 'English', region: 'Canada' },
  'en-AU': { language: 'English', region: 'Australia' },
  'es': { language: 'Spanish', region: 'Global' },
  'es-ES': { language: 'Spanish', region: 'Spain' },
  'es-MX': { language: 'Spanish', region: 'Mexico' },
  'fr': { language: 'French', region: 'Global' },
  'fr-FR': { language: 'French', region: 'France' },
  'fr-CA': { language: 'French', region: 'Canada' },
  'de': { language: 'German', region: 'Global' },
  'de-DE': { language: 'German', region: 'Germany' },
  'it': { language: 'Italian', region: 'Global' },
  'it-IT': { language: 'Italian', region: 'Italy' },
  'pt': { language: 'Portuguese', region: 'Global' },
  'pt-BR': { language: 'Portuguese', region: 'Brazil' },
  'ja': { language: 'Japanese', region: 'Global' },
  'ja-JP': { language: 'Japanese', region: 'Japan' },
  'ko': { language: 'Korean', region: 'Global' },
  'ko-KR': { language: 'Korean', region: 'South Korea' },
  'zh': { language: 'Chinese', region: 'Global' },
  'zh-CN': { language: 'Chinese', region: 'China' },
  'zh-TW': { language: 'Chinese', region: 'Taiwan' },
  'ru': { language: 'Russian', region: 'Global' },
  'ru-RU': { language: 'Russian', region: 'Russia' }
} as const

/**
 * Generate hreflang entries for a given page
 */
export function generateHreflangEntries(
  basePath: string,
  currentLocale: string = 'en',
  availableLocales: string[] = ['en']
): HreflangEntry[] {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'
  const entries: HreflangEntry[] = []
  
  // Add entries for each available locale
  availableLocales.forEach(locale => {
    const localeInfo = SUPPORTED_LOCALES[locale as keyof typeof SUPPORTED_LOCALES]
    if (!localeInfo) return
    
    const url = locale === 'en' 
      ? `${baseUrl}${basePath}`
      : `${baseUrl}/${locale}${basePath}`
    
    entries.push({
      hreflang: locale,
      href: url,
      language: localeInfo.language,
      region: localeInfo.region
    })
  })
  
  // Add x-default for the default language
  if (availableLocales.includes('en')) {
    entries.push({
      hreflang: 'x-default',
      href: `${baseUrl}${basePath}`,
      language: 'Default',
      region: 'Global'
    })
  }
  
  return entries
}

/**
 * Generate hreflang entries for product pages
 */
export function generateProductHreflangEntries(
  productId: string,
  productSlug: string,
  availableLocales: string[] = ['en']
): HreflangEntry[] {
  return generateHreflangEntries(
    `/products/${productId}/${productSlug}`,
    'en',
    availableLocales
  )
}

/**
 * Generate hreflang entries for community content
 */
export function generateCommunityHreflangEntries(
  contentType: 'discussions' | 'challenges' | 'submissions',
  contentId: string,
  contentSlug: string,
  availableLocales: string[] = ['en']
): HreflangEntry[] {
  return generateHreflangEntries(
    `/community/${contentType}/${contentId}/${contentSlug}`,
    'en',
    availableLocales
  )
}

/**
 * Generate hreflang entries for blog posts
 */
export function generateBlogHreflangEntries(
  blogSlug: string,
  availableLocales: string[] = ['en']
): HreflangEntry[] {
  return generateHreflangEntries(
    `/blog/${blogSlug}`,
    'en',
    availableLocales
  )
}

/**
 * Validate hreflang entries
 */
export function validateHreflangEntries(entries: HreflangEntry[]): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []
  
  // Check for duplicate hreflang values
  const hreflangs = entries.map(entry => entry.hreflang)
  const duplicates = hreflangs.filter((item, index) => hreflangs.indexOf(item) !== index)
  if (duplicates.length > 0) {
    errors.push(`Duplicate hreflang values found: ${duplicates.join(', ')}`)
  }
  
  // Check for valid hreflang format
  entries.forEach(entry => {
    if (entry.hreflang !== 'x-default' && !isValidHreflang(entry.hreflang)) {
      errors.push(`Invalid hreflang format: ${entry.hreflang}`)
    }
  })
  
  // Check for valid URLs
  entries.forEach(entry => {
    try {
      new URL(entry.href)
    } catch {
      errors.push(`Invalid URL: ${entry.href}`)
    }
  })
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Check if hreflang value is valid
 */
function isValidHreflang(hreflang: string): boolean {
  // Basic validation for language-region format
  const pattern = /^[a-z]{2}(-[A-Z]{2})?$/
  return pattern.test(hreflang)
}

/**
 * Get canonical URL for a locale
 */
export function getCanonicalUrlForLocale(
  basePath: string,
  locale: string = 'en'
): string {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'
  
  if (locale === 'en') {
    return `${baseUrl}${basePath}`
  }
  
  return `${baseUrl}/${locale}${basePath}`
}

/**
 * Generate alternate language links for HTML head
 */
export function generateAlternateLanguageLinks(entries: HreflangEntry[]): string[] {
  return entries.map(entry => 
    `<link rel="alternate" hreflang="${entry.hreflang}" href="${entry.href}" />`
  )
}

/**
 * Get user's preferred locale from browser
 */
export function getUserPreferredLocale(
  availableLocales: string[] = ['en'],
  fallback: string = 'en'
): string {
  if (typeof window === 'undefined') return fallback
  
  const browserLanguages = navigator.languages || [navigator.language]
  
  for (const browserLang of browserLanguages) {
    // Exact match
    if (availableLocales.includes(browserLang)) {
      return browserLang
    }
    
    // Language-only match (e.g., 'en' for 'en-US')
    const langOnly = browserLang.split('-')[0]
    if (availableLocales.includes(langOnly)) {
      return langOnly
    }
    
    // Region-specific match
    const regionSpecific = availableLocales.find(locale => 
      locale.startsWith(langOnly + '-')
    )
    if (regionSpecific) {
      return regionSpecific
    }
  }
  
  return fallback
}

/**
 * Generate sitemap entries for all locales
 */
export function generateLocalizedSitemapEntries(
  basePath: string,
  availableLocales: string[] = ['en'],
  lastModified?: Date,
  changeFrequency?: string,
  priority?: number
) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'
  
  return availableLocales.map(locale => ({
    url: locale === 'en' 
      ? `${baseUrl}${basePath}`
      : `${baseUrl}/${locale}${basePath}`,
    lastModified: lastModified || new Date(),
    changeFrequency: changeFrequency || 'weekly',
    priority: priority || 0.7,
    locale
  }))
}

/**
 * Get localized content metadata
 */
export function getLocalizedContentMetadata(
  content: any,
  locale: string = 'en'
): LocalizedContent {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'
  
  // Check if content has localized versions
  const localizedContent = content.localizations?.[locale] || content
  
  return {
    language: locale,
    region: SUPPORTED_LOCALES[locale as keyof typeof SUPPORTED_LOCALES]?.region,
    url: getCanonicalUrlForLocale(content.path || '/', locale),
    title: localizedContent.title || content.title,
    description: localizedContent.description || content.description
  }
}
