/**
 * Enhanced SEO Manager
 * 
 * Comprehensive SEO management system with advanced structured data,
 * dynamic meta tag generation, social media optimization, and performance tracking.
 * 
 * <AUTHOR> Team
 */

import { Metadata } from 'next'

/**
 * SEO configuration interfaces
 */
export interface SEOPageData {
  title: string
  description: string
  keywords?: string[]
  author?: {
    name: string
    url?: string
    image?: string
  }
  content?: {
    type: 'article' | 'product' | 'profile' | 'page' | 'event' | 'organization'
    category?: string
    tags?: string[]
    publishedAt?: Date
    updatedAt?: Date
    readingTime?: number
    wordCount?: number
  }
  images?: {
    url: string
    alt: string
    width?: number
    height?: number
  }[]
  url: string
  canonical?: string
  noIndex?: boolean
  priority?: number
  changeFrequency?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never'
}

export interface ProductSEOData extends SEOPageData {
  product: {
    name: string
    description: string
    price: number
    currency: string
    availability: 'InStock' | 'OutOfStock' | 'PreOrder' | 'Discontinued'
    condition: 'New' | 'Used' | 'Refurbished'
    brand: string
    category: string
    sku?: string
    gtin?: string
    mpn?: string
    reviews?: {
      count: number
      averageRating: number
    }
  }
}

export interface ArticleSEOData extends SEOPageData {
  article: {
    headline: string
    author: string
    publishedAt: Date
    updatedAt?: Date
    section: string
    tags: string[]
    wordCount: number
    readingTime: number
    featured?: boolean
  }
}

export interface EventSEOData extends SEOPageData {
  event: {
    name: string
    description: string
    startDate: Date
    endDate?: Date
    location?: {
      name: string
      address?: string
      city?: string
      country?: string
    }
    organizer: string
    eventType: 'Contest' | 'Workshop' | 'Meetup' | 'Sale' | 'Launch'
    price?: number
    currency?: string
    availability?: number
  }
}

/**
 * Enhanced SEO Manager Class
 */
export class EnhancedSEOManager {
  private static readonly SITE_NAME = 'Syndicaps'
  private static readonly SITE_URL = 'https://syndicaps.com'
  private static readonly DEFAULT_IMAGE = '/images/syndicaps-og-default.jpg'
  private static readonly TWITTER_HANDLE = '@syndicaps'
  private static readonly FACEBOOK_APP_ID = process.env.NEXT_PUBLIC_FACEBOOK_APP_ID
  
  // Brand keywords for consistent SEO
  private static readonly BRAND_KEYWORDS = [
    'syndicaps',
    'artisan keycaps',
    'mechanical keyboard',
    'custom keycaps',
    'keyboard accessories',
    'keycap community',
    'premium keycaps'
  ]

  /**
   * Generate comprehensive metadata for any page type
   */
  static generateMetadata(data: SEOPageData): Metadata {
    const title = this.generateTitle(data.title, data.content?.type)
    const description = this.optimizeDescription(data.description)
    const keywords = this.generateKeywords(data.keywords, data.content)
    const canonicalUrl = data.canonical || `${this.SITE_URL}${data.url}`
    const ogImage = this.selectBestImage(data.images)

    return {
      title,
      description,
      keywords: keywords.join(', '),
      
      // Authors
      ...(data.author && {
        authors: [{ name: data.author.name, url: data.author.url }]
      }),
      
      // Open Graph
      openGraph: {
        title,
        description,
        url: canonicalUrl,
        siteName: this.SITE_NAME,
        type: this.getOpenGraphType(data.content?.type),
        images: [
          {
            url: ogImage.url,
            width: ogImage.width || 1200,
            height: ogImage.height || 630,
            alt: ogImage.alt,
          }
        ],
        locale: 'en_US',
        ...(data.content?.publishedAt && {
          publishedTime: data.content.publishedAt.toISOString()
        }),
        ...(data.content?.updatedAt && {
          modifiedTime: data.content.updatedAt.toISOString()
        }),
        ...(data.author && {
          authors: [data.author.name]
        })
      },
      
      // Twitter
      twitter: {
        card: 'summary_large_image',
        site: this.TWITTER_HANDLE,
        creator: this.TWITTER_HANDLE,
        title,
        description,
        images: [ogImage.url]
      },
      
      // Additional meta tags
      other: {
        'og:image:alt': ogImage.alt,
        'twitter:image:alt': ogImage.alt,
        ...(this.FACEBOOK_APP_ID && {
          'fb:app_id': this.FACEBOOK_APP_ID
        }),
        ...(data.content?.category && {
          'article:section': data.content.category
        }),
        ...(data.content?.tags && {
          'article:tag': data.content.tags.join(',')
        }),
        ...(data.content?.readingTime && {
          'twitter:label1': 'Reading time',
          'twitter:data1': `${data.content.readingTime} min read`
        }),
        'theme-color': '#6366f1',
        'msapplication-TileColor': '#6366f1',
        'apple-mobile-web-app-capable': 'yes',
        'apple-mobile-web-app-status-bar-style': 'default'
      },
      
      // Canonical URL
      alternates: {
        canonical: canonicalUrl
      },
      
      // Robots
      robots: {
        index: !data.noIndex,
        follow: !data.noIndex,
        googleBot: {
          index: !data.noIndex,
          follow: !data.noIndex,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
      
      // Manifest
      manifest: '/manifest.json',
      
      // App-specific
      appleWebApp: {
        capable: true,
        statusBarStyle: 'default',
        title: this.SITE_NAME
      }
    }
  }

  /**
   * Generate structured data for products
   */
  static generateProductStructuredData(data: ProductSEOData): object[] {
    const structuredData = []
    
    // Product schema
    structuredData.push({
      '@context': 'https://schema.org',
      '@type': 'Product',
      name: data.product.name,
      description: data.product.description,
      image: data.images?.map(img => img.url) || [this.DEFAULT_IMAGE],
      brand: {
        '@type': 'Brand',
        name: data.product.brand
      },
      category: data.product.category,
      offers: {
        '@type': 'Offer',
        price: data.product.price,
        priceCurrency: data.product.currency,
        availability: `https://schema.org/${data.product.availability}`,
        itemCondition: `https://schema.org/${data.product.condition}Condition`,
        seller: {
          '@type': 'Organization',
          name: this.SITE_NAME
        }
      },
      ...(data.product.sku && { sku: data.product.sku }),
      ...(data.product.gtin && { gtin: data.product.gtin }),
      ...(data.product.mpn && { mpn: data.product.mpn }),
      ...(data.product.reviews && {
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: data.product.reviews.averageRating,
          reviewCount: data.product.reviews.count
        }
      })
    })

    // Organization schema
    structuredData.push(this.generateOrganizationSchema())
    
    // Website schema
    structuredData.push(this.generateWebsiteSchema())

    return structuredData
  }

  /**
   * Generate structured data for articles
   */
  static generateArticleStructuredData(data: ArticleSEOData): object[] {
    const structuredData = []
    
    // Article schema
    structuredData.push({
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: data.article.headline,
      description: data.description,
      image: data.images?.map(img => img.url) || [this.DEFAULT_IMAGE],
      author: {
        '@type': 'Person',
        name: data.article.author,
        ...(data.author?.url && { url: data.author.url })
      },
      publisher: {
        '@type': 'Organization',
        name: this.SITE_NAME,
        logo: {
          '@type': 'ImageObject',
          url: `${this.SITE_URL}/images/logo.png`
        }
      },
      datePublished: data.article.publishedAt.toISOString(),
      ...(data.article.updatedAt && {
        dateModified: data.article.updatedAt.toISOString()
      }),
      articleSection: data.article.section,
      keywords: data.keywords?.join(', '),
      wordCount: data.article.wordCount,
      timeRequired: `PT${data.article.readingTime}M`,
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': `${this.SITE_URL}${data.url}`
      }
    })

    // Organization schema
    structuredData.push(this.generateOrganizationSchema())

    return structuredData
  }

  /**
   * Generate comprehensive structured data for any page
   */
  static generateComprehensiveStructuredData(pageType: string, data: any): object[] {
    const structuredData = []

    // Always include organization schema
    structuredData.push(this.generateOrganizationSchema())

    // Always include website schema
    structuredData.push(this.generateWebsiteSchema())

    // Add page-specific schemas
    switch (pageType) {
      case 'product':
        if (data.product) {
          structuredData.push(...this.generateProductStructuredData(data.product))
        }
        break

      case 'article':
      case 'blog':
        if (data.article) {
          structuredData.push(...this.generateArticleStructuredData(data.article))
        }
        break

      case 'event':
        if (data.event) {
          structuredData.push(...this.generateEventStructuredData(data.event))
        }
        break

      case 'faq':
        if (data.faqs && data.faqs.length > 0) {
          structuredData.push(this.generateFAQSchema(data.faqs))
        }
        break

      case 'local-business':
        structuredData.push(this.generateLocalBusinessSchema())
        break
    }

    // Add breadcrumb if provided
    if (data.breadcrumb) {
      structuredData.push(this.generateBreadcrumbSchema(data.breadcrumb))
    }

    return structuredData
  }

  /**
   * Generate FAQ structured data
   */
  static generateFAQSchema(faqs: Array<{ question: string; answer: string }>): object {
    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: faqs.map(faq => ({
        '@type': 'Question',
        name: faq.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: faq.answer
        }
      }))
    }
  }

  /**
   * Generate breadcrumb structured data
   */
  static generateBreadcrumbSchema(breadcrumb: { items: Array<{ name: string; url: string }> }): object {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumb.items.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: item.name,
        item: item.url
      }))
    }
  }

  /**
   * Generate local business structured data
   */
  static generateLocalBusinessSchema(): object {
    return {
      '@context': 'https://schema.org',
      '@type': 'LocalBusiness',
      name: this.SITE_NAME,
      url: this.SITE_URL,
      logo: {
        '@type': 'ImageObject',
        url: `${this.SITE_URL}/logo.png`
      },
      description: 'Premium artisan keycaps and mechanical keyboard community platform',
      priceRange: '$10-$500',
      telephone: '******-KEYCAPS',
      email: '<EMAIL>',
      address: {
        '@type': 'PostalAddress',
        streetAddress: '123 Keycap Street',
        addressLocality: 'Tech City',
        addressRegion: 'CA',
        postalCode: '90210',
        addressCountry: 'US'
      },
      geo: {
        '@type': 'GeoCoordinates',
        latitude: 34.0522,
        longitude: -118.2437
      },
      openingHours: [
        'Mo-Fr 09:00-17:00',
        'Sa 10:00-16:00'
      ],
      sameAs: [
        'https://twitter.com/syndicaps',
        'https://instagram.com/syndicaps',
        'https://discord.gg/syndicaps',
        'https://facebook.com/syndicaps'
      ],
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: 4.8,
        reviewCount: 1250,
        bestRating: 5,
        worstRating: 1
      }
    }
  }

  /**
   * Generate structured data for events
   */
  static generateEventStructuredData(data: EventSEOData): object[] {
    const structuredData = []
    
    // Event schema
    structuredData.push({
      '@context': 'https://schema.org',
      '@type': 'Event',
      name: data.event.name,
      description: data.event.description,
      startDate: data.event.startDate.toISOString(),
      ...(data.event.endDate && {
        endDate: data.event.endDate.toISOString()
      }),
      eventStatus: 'https://schema.org/EventScheduled',
      eventAttendanceMode: 'https://schema.org/OnlineEventAttendanceMode',
      ...(data.event.location && {
        location: {
          '@type': 'Place',
          name: data.event.location.name,
          ...(data.event.location.address && {
            address: {
              '@type': 'PostalAddress',
              streetAddress: data.event.location.address,
              addressLocality: data.event.location.city,
              addressCountry: data.event.location.country
            }
          })
        }
      }),
      organizer: {
        '@type': 'Organization',
        name: data.event.organizer,
        url: this.SITE_URL
      },
      image: data.images?.map(img => img.url) || [this.DEFAULT_IMAGE],
      ...(data.event.price && {
        offers: {
          '@type': 'Offer',
          price: data.event.price,
          priceCurrency: data.event.currency || 'USD',
          availability: 'https://schema.org/InStock'
        }
      })
    })

    return structuredData
  }

  /**
   * Generate breadcrumb structured data
   */
  static generateBreadcrumbStructuredData(breadcrumbs: { name: string; url: string }[]): object {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumbs.map((crumb, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: crumb.name,
        item: `${this.SITE_URL}${crumb.url}`
      }))
    }
  }

  /**
   * Private helper methods
   */
  private static generateTitle(title: string, contentType?: string): string {
    // Avoid duplicate site name
    if (title.includes(this.SITE_NAME)) {
      return title
    }
    
    // Add content type context if relevant
    const typeContext = contentType === 'article' ? 'Blog' : 
                       contentType === 'product' ? 'Shop' : ''
    
    return typeContext ? 
      `${title} | ${typeContext} - ${this.SITE_NAME}` : 
      `${title} | ${this.SITE_NAME}`
  }

  private static optimizeDescription(description: string): string {
    // Ensure description is within optimal length (150-160 characters)
    if (description.length <= 160) {
      return description
    }
    
    // Truncate at word boundary
    const truncated = description.substring(0, 157)
    const lastSpace = truncated.lastIndexOf(' ')
    return truncated.substring(0, lastSpace) + '...'
  }

  private static generateKeywords(keywords: string[] = [], content?: SEOPageData['content']): string[] {
    const allKeywords = new Set([
      ...this.BRAND_KEYWORDS,
      ...keywords
    ])

    // Add content-specific keywords
    if (content?.tags) {
      content.tags.forEach(tag => allKeywords.add(tag.toLowerCase()))
    }
    
    if (content?.category) {
      allKeywords.add(content.category.toLowerCase())
    }

    return Array.from(allKeywords).slice(0, 15) // Limit to 15 keywords
  }

  private static selectBestImage(images?: SEOPageData['images']): { url: string; alt: string; width?: number; height?: number } {
    if (!images || images.length === 0) {
      return {
        url: this.DEFAULT_IMAGE,
        alt: `${this.SITE_NAME} - Premium Artisan Keycaps`,
        width: 1200,
        height: 630
      }
    }

    // Prefer images with optimal social media dimensions
    const socialOptimal = images.find(img => 
      img.width === 1200 && img.height === 630
    )
    
    if (socialOptimal) return socialOptimal
    
    // Fall back to first image
    return images[0]
  }

  private static getOpenGraphType(contentType?: string): string {
    switch (contentType) {
      case 'article': return 'article'
      case 'product': return 'product'
      case 'profile': return 'profile'
      default: return 'website'
    }
  }

  private static generateOrganizationSchema(): object {
    return {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: this.SITE_NAME,
      url: this.SITE_URL,
      logo: `${this.SITE_URL}/images/logo.png`,
      description: 'Premium artisan keycaps for mechanical keyboard enthusiasts',
      sameAs: [
        'https://twitter.com/syndicaps',
        'https://discord.gg/syndicaps',
        'https://instagram.com/syndicaps'
      ],
      contactPoint: {
        '@type': 'ContactPoint',
        contactType: 'customer service',
        email: '<EMAIL>'
      }
    }
  }

  private static generateWebsiteSchema(): object {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: this.SITE_NAME,
      url: this.SITE_URL,
      description: 'Premium artisan keycaps for mechanical keyboard enthusiasts',
      potentialAction: {
        '@type': 'SearchAction',
        target: `${this.SITE_URL}/search?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      }
    }
  }
}

// Export utility functions
export const seoManager = EnhancedSEOManager
