/**
 * Open Graph Image Generator
 * 
 * Utilities for generating dynamic Open Graph images for better
 * social media sharing and visual appeal.
 * 
 * <AUTHOR> Team
 */

export interface OGImageConfig {
  title: string
  description?: string
  image?: string
  logo?: string
  theme?: 'light' | 'dark' | 'purple'
  type?: 'product' | 'article' | 'profile' | 'community' | 'default'
  price?: string
  rating?: number
  author?: string
  category?: string
}

/**
 * Generate Open Graph image URL using dynamic image generation
 */
export function generateOGImageUrl(config: OGImageConfig): string {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'
  const params = new URLSearchParams()
  
  // Required parameters
  params.set('title', config.title)
  
  // Optional parameters
  if (config.description) {
    params.set('description', config.description.substring(0, 100))
  }
  
  if (config.image) {
    params.set('image', config.image)
  }
  
  if (config.logo) {
    params.set('logo', config.logo)
  }
  
  if (config.theme) {
    params.set('theme', config.theme)
  }
  
  if (config.type) {
    params.set('type', config.type)
  }
  
  if (config.price) {
    params.set('price', config.price)
  }
  
  if (config.rating) {
    params.set('rating', config.rating.toString())
  }
  
  if (config.author) {
    params.set('author', config.author)
  }
  
  if (config.category) {
    params.set('category', config.category)
  }
  
  return `${baseUrl}/api/og?${params.toString()}`
}

/**
 * Generate product-specific OG image
 */
export function generateProductOGImage(product: any): string {
  return generateOGImageUrl({
    title: product.name,
    description: product.description,
    image: product.image || product.images?.[0],
    type: 'product',
    price: product.price ? `$${product.price}` : undefined,
    rating: product.rating?.average,
    category: product.category,
    theme: 'purple'
  })
}

/**
 * Generate article-specific OG image
 */
export function generateArticleOGImage(article: any): string {
  return generateOGImageUrl({
    title: article.title,
    description: article.excerpt || article.description,
    image: article.featuredImage || article.image,
    type: 'article',
    author: article.author?.name,
    category: article.category,
    theme: 'dark'
  })
}

/**
 * Generate community content OG image
 */
export function generateCommunityOGImage(content: any): string {
  return generateOGImageUrl({
    title: content.title,
    description: content.description || content.excerpt,
    image: content.image,
    type: 'community',
    author: content.author?.name || content.creator?.name,
    category: content.category || 'Community',
    theme: 'purple'
  })
}

/**
 * Generate profile OG image
 */
export function generateProfileOGImage(profile: any): string {
  return generateOGImageUrl({
    title: profile.displayName || profile.username,
    description: profile.bio || `${profile.displayName} on Syndicaps`,
    image: profile.avatar || profile.profileImage,
    type: 'profile',
    theme: 'light'
  })
}

/**
 * Generate default OG image for pages without specific content
 */
export function generateDefaultOGImage(title: string, description?: string): string {
  return generateOGImageUrl({
    title,
    description,
    type: 'default',
    theme: 'purple'
  })
}

/**
 * Get optimized image dimensions for different social platforms
 */
export function getOptimizedImageDimensions(platform: string) {
  const dimensions = {
    facebook: { width: 1200, height: 630 },
    twitter: { width: 1200, height: 675 },
    linkedin: { width: 1200, height: 627 },
    instagram: { width: 1080, height: 1080 },
    pinterest: { width: 1000, height: 1500 },
    default: { width: 1200, height: 630 }
  }
  
  return dimensions[platform as keyof typeof dimensions] || dimensions.default
}

/**
 * Generate multiple OG images for different platforms
 */
export function generateMultiPlatformOGImages(config: OGImageConfig) {
  const platforms = ['facebook', 'twitter', 'linkedin']
  
  return platforms.reduce((images, platform) => {
    const dimensions = getOptimizedImageDimensions(platform)
    const params = new URLSearchParams()
    
    // Add all config parameters
    params.set('title', config.title)
    params.set('width', dimensions.width.toString())
    params.set('height', dimensions.height.toString())
    params.set('platform', platform)
    
    if (config.description) params.set('description', config.description)
    if (config.image) params.set('image', config.image)
    if (config.theme) params.set('theme', config.theme)
    if (config.type) params.set('type', config.type)
    
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'
    images[platform] = `${baseUrl}/api/og?${params.toString()}`
    
    return images
  }, {} as Record<string, string>)
}

/**
 * Validate and sanitize OG image config
 */
export function sanitizeOGImageConfig(config: Partial<OGImageConfig>): OGImageConfig {
  return {
    title: config.title?.substring(0, 60) || 'Syndicaps',
    description: config.description?.substring(0, 100),
    image: config.image,
    logo: config.logo || '/logo.png',
    theme: config.theme || 'purple',
    type: config.type || 'default',
    price: config.price,
    rating: config.rating && config.rating >= 0 && config.rating <= 5 ? config.rating : undefined,
    author: config.author?.substring(0, 30),
    category: config.category?.substring(0, 20)
  }
}

/**
 * Generate OG image with fallback
 */
export function generateOGImageWithFallback(config: Partial<OGImageConfig>): string {
  try {
    const sanitizedConfig = sanitizeOGImageConfig(config)
    return generateOGImageUrl(sanitizedConfig)
  } catch (error) {
    console.error('Error generating OG image:', error)
    // Return default Syndicaps OG image
    return `${process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'}/images/syndicaps-og-default.jpg`
  }
}

/**
 * Cache key generator for OG images
 */
export function generateOGImageCacheKey(config: OGImageConfig): string {
  const keyData = {
    title: config.title,
    type: config.type,
    theme: config.theme,
    hasImage: !!config.image,
    hasPrice: !!config.price,
    hasRating: !!config.rating
  }
  
  return btoa(JSON.stringify(keyData)).replace(/[^a-zA-Z0-9]/g, '').substring(0, 32)
}

/**
 * Preload OG images for better performance
 */
export function preloadOGImages(configs: OGImageConfig[]) {
  if (typeof window === 'undefined') return
  
  configs.forEach(config => {
    const imageUrl = generateOGImageUrl(config)
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'image'
    link.href = imageUrl
    document.head.appendChild(link)
  })
}
