/**
 * SEO Analytics and Monitoring
 * 
 * Comprehensive SEO performance tracking and monitoring tools
 * for measuring and improving search engine optimization.
 * 
 * <AUTHOR> Team
 */

export interface SEOMetrics {
  pageUrl: string
  title: string
  description: string
  keywords: string[]
  
  // Technical SEO
  loadTime: number
  mobileScore: number
  desktopScore: number
  coreWebVitals: {
    lcp: number // Largest Contentful Paint
    fid: number // First Input Delay
    cls: number // Cumulative Layout Shift
  }
  
  // Content metrics
  wordCount: number
  headingStructure: {
    h1: number
    h2: number
    h3: number
    h4: number
    h5: number
    h6: number
  }
  imageCount: number
  linkCount: {
    internal: number
    external: number
  }
  
  // Social metrics
  socialShares: {
    facebook: number
    twitter: number
    linkedin: number
    pinterest: number
    total: number
  }
  
  // Search metrics
  impressions: number
  clicks: number
  ctr: number
  averagePosition: number
  
  // Timestamp
  timestamp: Date
}

export interface SEOIssue {
  type: 'error' | 'warning' | 'info'
  category: 'technical' | 'content' | 'meta' | 'links' | 'images' | 'performance'
  message: string
  element?: string
  recommendation: string
  priority: 'high' | 'medium' | 'low'
}

export interface SEOAuditResult {
  url: string
  score: number
  issues: SEOIssue[]
  metrics: SEOMetrics
  recommendations: string[]
  timestamp: Date
}

/**
 * SEO Analytics Manager
 */
export class SEOAnalytics {
  private static instance: SEOAnalytics
  private metrics: Map<string, SEOMetrics[]> = new Map()
  
  static getInstance(): SEOAnalytics {
    if (!SEOAnalytics.instance) {
      SEOAnalytics.instance = new SEOAnalytics()
    }
    return SEOAnalytics.instance
  }
  
  /**
   * Track page view for SEO analytics
   */
  trackPageView(url: string, additionalData?: Partial<SEOMetrics>): void {
    if (typeof window === 'undefined') return
    
    const metrics: Partial<SEOMetrics> = {
      pageUrl: url,
      title: document.title,
      description: this.getMetaDescription(),
      keywords: this.getMetaKeywords(),
      timestamp: new Date(),
      ...additionalData
    }
    
    // Collect technical metrics
    this.collectTechnicalMetrics(metrics)
    
    // Collect content metrics
    this.collectContentMetrics(metrics)
    
    // Store metrics
    this.storeMetrics(url, metrics as SEOMetrics)
    
    // Send to analytics endpoint
    this.sendToAnalytics(metrics)
  }
  
  /**
   * Perform comprehensive SEO audit
   */
  async performSEOAudit(url?: string): Promise<SEOAuditResult> {
    const targetUrl = url || (typeof window !== 'undefined' ? window.location.href : '')
    const issues: SEOIssue[] = []
    
    // Technical SEO checks
    issues.push(...this.checkTechnicalSEO())
    
    // Content SEO checks
    issues.push(...this.checkContentSEO())
    
    // Meta tag checks
    issues.push(...this.checkMetaTags())
    
    // Link checks
    issues.push(...this.checkLinks())
    
    // Image checks
    issues.push(...this.checkImages())
    
    // Performance checks
    issues.push(...await this.checkPerformance())
    
    // Calculate overall score
    const score = this.calculateSEOScore(issues)
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(issues)
    
    // Get current metrics
    const metrics = this.getCurrentMetrics()
    
    return {
      url: targetUrl,
      score,
      issues,
      metrics,
      recommendations,
      timestamp: new Date()
    }
  }
  
  /**
   * Get meta description from document
   */
  private getMetaDescription(): string {
    if (typeof window === 'undefined') return ''
    const meta = document.querySelector('meta[name="description"]') as HTMLMetaElement
    return meta?.content || ''
  }
  
  /**
   * Get meta keywords from document
   */
  private getMetaKeywords(): string[] {
    if (typeof window === 'undefined') return []
    const meta = document.querySelector('meta[name="keywords"]') as HTMLMetaElement
    return meta?.content.split(',').map(k => k.trim()) || []
  }
  
  /**
   * Collect technical metrics
   */
  private collectTechnicalMetrics(metrics: Partial<SEOMetrics>): void {
    if (typeof window === 'undefined') return
    
    // Core Web Vitals
    if ('web-vitals' in window) {
      // This would integrate with web-vitals library
      metrics.coreWebVitals = {
        lcp: 0, // Would be populated by web-vitals
        fid: 0,
        cls: 0
      }
    }
    
    // Load time
    if (performance.timing) {
      metrics.loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart
    }
  }
  
  /**
   * Collect content metrics
   */
  private collectContentMetrics(metrics: Partial<SEOMetrics>): void {
    if (typeof window === 'undefined') return
    
    // Word count
    const textContent = document.body.textContent || ''
    metrics.wordCount = textContent.split(/\s+/).filter(word => word.length > 0).length
    
    // Heading structure
    metrics.headingStructure = {
      h1: document.querySelectorAll('h1').length,
      h2: document.querySelectorAll('h2').length,
      h3: document.querySelectorAll('h3').length,
      h4: document.querySelectorAll('h4').length,
      h5: document.querySelectorAll('h5').length,
      h6: document.querySelectorAll('h6').length
    }
    
    // Image count
    metrics.imageCount = document.querySelectorAll('img').length
    
    // Link count
    const links = document.querySelectorAll('a[href]')
    let internal = 0
    let external = 0
    
    links.forEach(link => {
      const href = (link as HTMLAnchorElement).href
      if (href.startsWith(window.location.origin)) {
        internal++
      } else if (href.startsWith('http')) {
        external++
      }
    })
    
    metrics.linkCount = { internal, external }
  }
  
  /**
   * Check technical SEO issues
   */
  private checkTechnicalSEO(): SEOIssue[] {
    const issues: SEOIssue[] = []
    
    if (typeof window === 'undefined') return issues
    
    // Check for canonical URL
    const canonical = document.querySelector('link[rel="canonical"]')
    if (!canonical) {
      issues.push({
        type: 'warning',
        category: 'technical',
        message: 'Missing canonical URL',
        recommendation: 'Add a canonical URL to prevent duplicate content issues',
        priority: 'medium'
      })
    }
    
    // Check for meta viewport
    const viewport = document.querySelector('meta[name="viewport"]')
    if (!viewport) {
      issues.push({
        type: 'error',
        category: 'technical',
        message: 'Missing viewport meta tag',
        recommendation: 'Add viewport meta tag for mobile optimization',
        priority: 'high'
      })
    }
    
    // Check for robots meta
    const robots = document.querySelector('meta[name="robots"]')
    if (!robots) {
      issues.push({
        type: 'info',
        category: 'technical',
        message: 'Missing robots meta tag',
        recommendation: 'Consider adding robots meta tag for crawler guidance',
        priority: 'low'
      })
    }
    
    return issues
  }
  
  /**
   * Check content SEO issues
   */
  private checkContentSEO(): SEOIssue[] {
    const issues: SEOIssue[] = []
    
    if (typeof window === 'undefined') return issues
    
    // Check H1 tags
    const h1Tags = document.querySelectorAll('h1')
    if (h1Tags.length === 0) {
      issues.push({
        type: 'error',
        category: 'content',
        message: 'Missing H1 tag',
        recommendation: 'Add an H1 tag to define the main heading',
        priority: 'high'
      })
    } else if (h1Tags.length > 1) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: 'Multiple H1 tags found',
        recommendation: 'Use only one H1 tag per page',
        priority: 'medium'
      })
    }
    
    // Check content length
    const textContent = document.body.textContent || ''
    const wordCount = textContent.split(/\s+/).filter(word => word.length > 0).length
    
    if (wordCount < 300) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: 'Content is too short',
        recommendation: 'Add more content to improve SEO value (aim for 300+ words)',
        priority: 'medium'
      })
    }
    
    return issues
  }
  
  /**
   * Check meta tag issues
   */
  private checkMetaTags(): SEOIssue[] {
    const issues: SEOIssue[] = []
    
    if (typeof window === 'undefined') return issues
    
    // Check title tag
    const title = document.title
    if (!title) {
      issues.push({
        type: 'error',
        category: 'meta',
        message: 'Missing title tag',
        recommendation: 'Add a descriptive title tag',
        priority: 'high'
      })
    } else if (title.length < 30 || title.length > 60) {
      issues.push({
        type: 'warning',
        category: 'meta',
        message: 'Title length not optimal',
        recommendation: 'Keep title between 30-60 characters',
        priority: 'medium'
      })
    }
    
    // Check meta description
    const description = this.getMetaDescription()
    if (!description) {
      issues.push({
        type: 'error',
        category: 'meta',
        message: 'Missing meta description',
        recommendation: 'Add a compelling meta description',
        priority: 'high'
      })
    } else if (description.length < 120 || description.length > 160) {
      issues.push({
        type: 'warning',
        category: 'meta',
        message: 'Meta description length not optimal',
        recommendation: 'Keep meta description between 120-160 characters',
        priority: 'medium'
      })
    }
    
    return issues
  }
  
  /**
   * Check link issues
   */
  private checkLinks(): SEOIssue[] {
    const issues: SEOIssue[] = []
    
    if (typeof window === 'undefined') return issues
    
    // Check for broken links (simplified check)
    const links = document.querySelectorAll('a[href]')
    links.forEach(link => {
      const href = (link as HTMLAnchorElement).href
      if (href === '#' || href === 'javascript:void(0)') {
        issues.push({
          type: 'warning',
          category: 'links',
          message: 'Empty or placeholder link found',
          element: href,
          recommendation: 'Replace placeholder links with meaningful URLs',
          priority: 'low'
        })
      }
    })
    
    return issues
  }
  
  /**
   * Check image issues
   */
  private checkImages(): SEOIssue[] {
    const issues: SEOIssue[] = []
    
    if (typeof window === 'undefined') return issues
    
    // Check for missing alt text
    const images = document.querySelectorAll('img')
    let missingAltCount = 0
    
    images.forEach(img => {
      if (!img.alt) {
        missingAltCount++
      }
    })
    
    if (missingAltCount > 0) {
      issues.push({
        type: 'warning',
        category: 'images',
        message: `${missingAltCount} images missing alt text`,
        recommendation: 'Add descriptive alt text to all images for accessibility and SEO',
        priority: 'medium'
      })
    }
    
    return issues
  }
  
  /**
   * Check performance issues
   */
  private async checkPerformance(): Promise<SEOIssue[]> {
    const issues: SEOIssue[] = []
    
    if (typeof window === 'undefined') return issues
    
    // Check load time
    if (performance.timing) {
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart
      if (loadTime > 3000) {
        issues.push({
          type: 'warning',
          category: 'performance',
          message: 'Page load time is slow',
          recommendation: 'Optimize images, minify CSS/JS, and improve server response time',
          priority: 'high'
        })
      }
    }
    
    return issues
  }
  
  /**
   * Calculate overall SEO score
   */
  private calculateSEOScore(issues: SEOIssue[]): number {
    let score = 100
    
    issues.forEach(issue => {
      switch (issue.priority) {
        case 'high':
          score -= issue.type === 'error' ? 15 : 10
          break
        case 'medium':
          score -= issue.type === 'error' ? 8 : 5
          break
        case 'low':
          score -= issue.type === 'error' ? 3 : 2
          break
      }
    })
    
    return Math.max(0, score)
  }
  
  /**
   * Generate recommendations based on issues
   */
  private generateRecommendations(issues: SEOIssue[]): string[] {
    const recommendations = new Set<string>()
    
    issues.forEach(issue => {
      if (issue.priority === 'high') {
        recommendations.add(issue.recommendation)
      }
    })
    
    return Array.from(recommendations)
  }
  
  /**
   * Get current page metrics
   */
  private getCurrentMetrics(): SEOMetrics {
    const metrics: Partial<SEOMetrics> = {
      pageUrl: typeof window !== 'undefined' ? window.location.href : '',
      title: typeof window !== 'undefined' ? document.title : '',
      description: this.getMetaDescription(),
      keywords: this.getMetaKeywords(),
      timestamp: new Date()
    }
    
    this.collectTechnicalMetrics(metrics)
    this.collectContentMetrics(metrics)
    
    return metrics as SEOMetrics
  }
  
  /**
   * Store metrics locally
   */
  private storeMetrics(url: string, metrics: SEOMetrics): void {
    const existing = this.metrics.get(url) || []
    existing.push(metrics)
    
    // Keep only last 10 entries per URL
    if (existing.length > 10) {
      existing.splice(0, existing.length - 10)
    }
    
    this.metrics.set(url, existing)
  }
  
  /**
   * Send metrics to analytics endpoint
   */
  private async sendToAnalytics(metrics: Partial<SEOMetrics>): Promise<void> {
    try {
      await fetch('/api/analytics/seo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(metrics)
      })
    } catch (error) {
      console.error('Failed to send SEO analytics:', error)
    }
  }
  
  /**
   * Get historical metrics for a URL
   */
  getMetricsHistory(url: string): SEOMetrics[] {
    return this.metrics.get(url) || []
  }
  
  /**
   * Export all metrics
   */
  exportMetrics(): Record<string, SEOMetrics[]> {
    return Object.fromEntries(this.metrics.entries())
  }

  /**
   * Initialize SEO monitoring
   */
  static initializeMonitoring(): void {
    if (typeof window === 'undefined') return

    const analytics = SEOAnalytics.getInstance()

    // Track initial page load
    analytics.trackPageView(window.location.href)

    // Track navigation changes
    const originalPushState = history.pushState
    const originalReplaceState = history.replaceState

    history.pushState = function(...args) {
      originalPushState.apply(history, args)
      setTimeout(() => analytics.trackPageView(window.location.href), 100)
    }

    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args)
      setTimeout(() => analytics.trackPageView(window.location.href), 100)
    }

    // Track back/forward navigation
    window.addEventListener('popstate', () => {
      setTimeout(() => analytics.trackPageView(window.location.href), 100)
    })
  }
}
