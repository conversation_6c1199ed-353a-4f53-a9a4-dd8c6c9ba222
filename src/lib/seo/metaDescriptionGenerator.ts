/**
 * Meta Description Generator
 * 
 * Utilities for generating optimized meta descriptions that improve
 * click-through rates and search engine rankings.
 * 
 * <AUTHOR> Team
 */

export interface MetaDescriptionConfig {
  content: string
  type?: 'product' | 'article' | 'category' | 'profile' | 'homepage' | 'search'
  keywords?: string[]
  callToAction?: string
  price?: string
  rating?: number
  availability?: string
  brand?: string
  category?: string
  author?: string
  publishDate?: Date
  locale?: string
}

/**
 * Optimal meta description length ranges
 */
const META_DESCRIPTION_LIMITS = {
  min: 120,
  max: 160,
  ideal: 155
}

/**
 * Call-to-action phrases for different content types
 */
const CTA_PHRASES = {
  product: [
    'Shop now',
    'Buy today',
    'Get yours',
    'Order now',
    'Add to cart',
    'Discover more'
  ],
  article: [
    'Read more',
    'Learn more',
    'Discover',
    'Find out',
    'Explore',
    'Get insights'
  ],
  category: [
    'Browse collection',
    'Explore products',
    'Shop category',
    'View all',
    'Discover options'
  ],
  profile: [
    'View profile',
    'Connect',
    'Follow',
    'See work',
    'Explore'
  ],
  homepage: [
    'Explore',
    'Discover',
    'Join us',
    'Get started',
    'Shop now'
  ],
  search: [
    'Find products',
    'Search now',
    'Explore results',
    'Discover'
  ]
}

/**
 * Generate optimized meta description
 */
export function generateMetaDescription(config: MetaDescriptionConfig): string {
  const {
    content,
    type = 'article',
    keywords = [],
    callToAction,
    price,
    rating,
    availability,
    brand = 'Syndicaps',
    category,
    author,
    publishDate,
    locale = 'en'
  } = config

  // Start with base content
  let description = cleanContent(content)
  
  // Add type-specific enhancements
  switch (type) {
    case 'product':
      description = enhanceProductDescription(description, {
        price,
        rating,
        availability,
        brand,
        category
      })
      break
      
    case 'article':
      description = enhanceArticleDescription(description, {
        author,
        publishDate,
        category
      })
      break
      
    case 'category':
      description = enhanceCategoryDescription(description, {
        category,
        brand
      })
      break
      
    case 'profile':
      description = enhanceProfileDescription(description, {
        author
      })
      break
      
    case 'homepage':
      description = enhanceHomepageDescription(description, {
        brand
      })
      break
      
    case 'search':
      description = enhanceSearchDescription(description)
      break
  }
  
  // Add keywords naturally
  description = incorporateKeywords(description, keywords)
  
  // Add call-to-action
  const cta = callToAction || getRandomCTA(type)
  description = addCallToAction(description, cta)
  
  // Optimize length
  description = optimizeLength(description)
  
  // Final cleanup
  description = finalizeDescription(description)
  
  return description
}

/**
 * Clean and prepare content
 */
function cleanContent(content: string): string {
  return content
    .replace(/\s+/g, ' ')
    .replace(/[^\w\s.,!?-]/g, '')
    .trim()
}

/**
 * Enhance product descriptions
 */
function enhanceProductDescription(
  description: string,
  options: {
    price?: string
    rating?: number
    availability?: string
    brand?: string
    category?: string
  }
): string {
  const { price, rating, availability, brand, category } = options
  
  let enhanced = description
  
  // Add price information
  if (price) {
    enhanced = `${enhanced} Starting at ${price}.`
  }
  
  // Add rating information
  if (rating && rating >= 4.0) {
    enhanced = `${enhanced} Rated ${rating}/5 stars.`
  }
  
  // Add availability
  if (availability === 'InStock') {
    enhanced = `${enhanced} In stock and ready to ship.`
  } else if (availability === 'PreOrder') {
    enhanced = `${enhanced} Available for pre-order.`
  }
  
  // Add category context
  if (category) {
    enhanced = `Premium ${category.toLowerCase()} from ${brand}. ${enhanced}`
  }
  
  return enhanced
}

/**
 * Enhance article descriptions
 */
function enhanceArticleDescription(
  description: string,
  options: {
    author?: string
    publishDate?: Date
    category?: string
  }
): string {
  const { author, publishDate, category } = options
  
  let enhanced = description
  
  // Add author information
  if (author) {
    enhanced = `${enhanced} By ${author}.`
  }
  
  // Add category context
  if (category) {
    enhanced = `${category} guide: ${enhanced}`
  }
  
  // Add freshness indicator for recent content
  if (publishDate) {
    const daysSincePublished = (Date.now() - publishDate.getTime()) / (1000 * 60 * 60 * 24)
    if (daysSincePublished < 7) {
      enhanced = `Latest: ${enhanced}`
    }
  }
  
  return enhanced
}

/**
 * Enhance category descriptions
 */
function enhanceCategoryDescription(
  description: string,
  options: {
    category?: string
    brand?: string
  }
): string {
  const { category, brand } = options
  
  if (category && brand) {
    return `Discover ${category.toLowerCase()} at ${brand}. ${description} Premium quality and fast shipping.`
  }
  
  return description
}

/**
 * Enhance profile descriptions
 */
function enhanceProfileDescription(
  description: string,
  options: {
    author?: string
  }
): string {
  const { author } = options
  
  if (author) {
    return `Meet ${author} on Syndicaps. ${description} Connect with the mechanical keyboard community.`
  }
  
  return description
}

/**
 * Enhance homepage descriptions
 */
function enhanceHomepageDescription(
  description: string,
  options: {
    brand?: string
  }
): string {
  const { brand } = options
  
  return `Welcome to ${brand}. ${description} Join the premium keycap community today.`
}

/**
 * Enhance search descriptions
 */
function enhanceSearchDescription(description: string): string {
  return `Search results: ${description} Find exactly what you're looking for.`
}

/**
 * Incorporate keywords naturally
 */
function incorporateKeywords(description: string, keywords: string[]): string {
  if (keywords.length === 0) return description
  
  // Try to incorporate 1-2 keywords naturally
  const primaryKeywords = keywords.slice(0, 2)
  let enhanced = description
  
  primaryKeywords.forEach(keyword => {
    const keywordLower = keyword.toLowerCase()
    const descriptionLower = enhanced.toLowerCase()
    
    // Only add if keyword isn't already present
    if (!descriptionLower.includes(keywordLower)) {
      // Try to add naturally at the beginning or end
      if (enhanced.length < META_DESCRIPTION_LIMITS.ideal - keyword.length - 10) {
        enhanced = `${keyword} - ${enhanced}`
      }
    }
  })
  
  return enhanced
}

/**
 * Add call-to-action
 */
function addCallToAction(description: string, cta: string): string {
  if (!cta) return description
  
  // Check if there's room for CTA
  if (description.length < META_DESCRIPTION_LIMITS.ideal - cta.length - 5) {
    return `${description} ${cta}!`
  }
  
  return description
}

/**
 * Get random call-to-action for content type
 */
function getRandomCTA(type: keyof typeof CTA_PHRASES): string {
  const phrases = CTA_PHRASES[type] || CTA_PHRASES.article
  return phrases[Math.floor(Math.random() * phrases.length)]
}

/**
 * Optimize description length
 */
function optimizeLength(description: string): string {
  if (description.length <= META_DESCRIPTION_LIMITS.max) {
    return description
  }
  
  // Truncate at word boundary
  const truncated = description.substring(0, META_DESCRIPTION_LIMITS.max)
  const lastSpace = truncated.lastIndexOf(' ')
  
  if (lastSpace > META_DESCRIPTION_LIMITS.min) {
    return truncated.substring(0, lastSpace) + '...'
  }
  
  return truncated + '...'
}

/**
 * Finalize description
 */
function finalizeDescription(description: string): string {
  return description
    .replace(/\s+/g, ' ')
    .replace(/\.\.\./g, '…')
    .trim()
}

/**
 * Validate meta description
 */
export function validateMetaDescription(description: string): {
  isValid: boolean
  length: number
  warnings: string[]
  suggestions: string[]
} {
  const warnings: string[] = []
  const suggestions: string[] = []
  const length = description.length
  
  // Length validation
  if (length < META_DESCRIPTION_LIMITS.min) {
    warnings.push(`Description is too short (${length} chars). Minimum recommended: ${META_DESCRIPTION_LIMITS.min}`)
    suggestions.push('Add more descriptive content to reach optimal length')
  }
  
  if (length > META_DESCRIPTION_LIMITS.max) {
    warnings.push(`Description is too long (${length} chars). Maximum recommended: ${META_DESCRIPTION_LIMITS.max}`)
    suggestions.push('Shorten description to prevent truncation in search results')
  }
  
  // Content validation
  if (!description.includes('.') && !description.includes('!') && !description.includes('?')) {
    suggestions.push('Consider adding punctuation for better readability')
  }
  
  if (description.toLowerCase().includes('click here')) {
    warnings.push('Avoid generic phrases like "click here"')
    suggestions.push('Use more specific call-to-action phrases')
  }
  
  // Keyword stuffing check
  const words = description.toLowerCase().split(/\s+/)
  const wordCounts = words.reduce((acc, word) => {
    acc[word] = (acc[word] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  const repeatedWords = Object.entries(wordCounts)
    .filter(([word, count]) => count > 3 && word.length > 3)
    .map(([word]) => word)
  
  if (repeatedWords.length > 0) {
    warnings.push(`Possible keyword stuffing detected: ${repeatedWords.join(', ')}`)
    suggestions.push('Vary your language to avoid repetition')
  }
  
  return {
    isValid: warnings.length === 0,
    length,
    warnings,
    suggestions
  }
}
