/**
 * Canonical URL Utilities
 * 
 * Utilities for generating and managing canonical URLs to prevent
 * duplicate content issues and improve SEO.
 * 
 * <AUTHOR> Team
 */

export interface CanonicalConfig {
  path: string
  params?: Record<string, string | string[]>
  excludeParams?: string[]
  forceHttps?: boolean
  trailingSlash?: boolean
  locale?: string
}

/**
 * Parameters that should be excluded from canonical URLs by default
 */
const DEFAULT_EXCLUDED_PARAMS = [
  // Analytics and tracking
  'utm_source',
  'utm_medium',
  'utm_campaign',
  'utm_term',
  'utm_content',
  'gclid',
  'fbclid',
  'ref',
  'source',
  'campaign',
  
  // Session and user-specific
  'sessionid',
  'userid',
  'token',
  'auth',
  
  // Pagination and sorting (usually)
  'page',
  'offset',
  'limit',
  'sort',
  'order',
  
  // UI state
  'tab',
  'modal',
  'popup',
  'view',
  
  // Timestamps and cache busting
  'timestamp',
  'cache',
  'v',
  'version',
  
  // Social sharing
  'share',
  'shared',
  'via'
]

/**
 * Parameters that should be preserved in canonical URLs
 */
const PRESERVED_PARAMS = [
  // Product filtering
  'category',
  'brand',
  'color',
  'size',
  'material',
  'profile',
  
  // Search
  'q',
  'query',
  'search',
  
  // Content filtering
  'type',
  'status',
  'featured',
  
  // Geographic
  'region',
  'country',
  'locale'
]

/**
 * Generate canonical URL for a given path and parameters
 */
export function generateCanonicalUrl(config: CanonicalConfig): string {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'
  let { path, params = {}, excludeParams = [], forceHttps = true, trailingSlash = false, locale } = config
  
  // Ensure path starts with /
  if (!path.startsWith('/')) {
    path = '/' + path
  }
  
  // Add locale prefix if specified and not default
  if (locale && locale !== 'en') {
    path = `/${locale}${path}`
  }
  
  // Clean up the path
  path = cleanPath(path)
  
  // Filter parameters
  const filteredParams = filterCanonicalParams(params, excludeParams)
  
  // Build URL
  let url = baseUrl + path
  
  // Force HTTPS if required
  if (forceHttps && url.startsWith('http://')) {
    url = url.replace('http://', 'https://')
  }
  
  // Add trailing slash if required
  if (trailingSlash && !path.endsWith('/') && !path.includes('.')) {
    url += '/'
  }
  
  // Add query parameters
  const queryString = buildQueryString(filteredParams)
  if (queryString) {
    url += '?' + queryString
  }
  
  return url
}

/**
 * Clean and normalize path
 */
function cleanPath(path: string): string {
  return path
    // Remove double slashes
    .replace(/\/+/g, '/')
    // Remove trailing slash (unless root)
    .replace(/\/$/, '') || '/'
}

/**
 * Filter parameters for canonical URL
 */
function filterCanonicalParams(
  params: Record<string, string | string[]>,
  additionalExcludes: string[] = []
): Record<string, string | string[]> {
  const excludeSet = new Set([
    ...DEFAULT_EXCLUDED_PARAMS,
    ...additionalExcludes
  ])
  
  const filtered: Record<string, string | string[]> = {}
  
  Object.entries(params).forEach(([key, value]) => {
    // Skip excluded parameters
    if (excludeSet.has(key.toLowerCase())) {
      return
    }
    
    // Include preserved parameters
    if (PRESERVED_PARAMS.includes(key.toLowerCase())) {
      filtered[key] = value
      return
    }
    
    // Include parameters that affect content
    if (isContentAffectingParam(key, value)) {
      filtered[key] = value
    }
  })
  
  return filtered
}

/**
 * Check if parameter affects content and should be in canonical URL
 */
function isContentAffectingParam(key: string, value: string | string[]): boolean {
  const keyLower = key.toLowerCase()
  
  // Always include these types of parameters
  const contentParams = [
    'id',
    'slug',
    'category',
    'tag',
    'filter',
    'search',
    'query',
    'type',
    'status'
  ]
  
  return contentParams.some(param => keyLower.includes(param))
}

/**
 * Build query string from parameters
 */
function buildQueryString(params: Record<string, string | string[]>): string {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      value.forEach(v => searchParams.append(key, v))
    } else {
      searchParams.set(key, value)
    }
  })
  
  return searchParams.toString()
}

/**
 * Generate canonical URL for product pages
 */
export function generateProductCanonicalUrl(
  productId: string,
  productSlug: string,
  params?: Record<string, string | string[]>
): string {
  return generateCanonicalUrl({
    path: `/products/${productId}/${productSlug}`,
    params,
    excludeParams: ['variant', 'color', 'size'] // These might be preserved for some products
  })
}

/**
 * Generate canonical URL for shop pages
 */
export function generateShopCanonicalUrl(
  params?: Record<string, string | string[]>
): string {
  return generateCanonicalUrl({
    path: '/shop',
    params,
    excludeParams: ['page', 'sort', 'view'] // Pagination and sorting usually excluded
  })
}

/**
 * Generate canonical URL for community content
 */
export function generateCommunityCanonicalUrl(
  contentType: 'discussions' | 'challenges' | 'submissions',
  contentId: string,
  contentSlug: string,
  params?: Record<string, string | string[]>
): string {
  return generateCanonicalUrl({
    path: `/community/${contentType}/${contentId}/${contentSlug}`,
    params,
    excludeParams: ['comment', 'reply', 'highlight']
  })
}

/**
 * Generate canonical URL for blog posts
 */
export function generateBlogCanonicalUrl(
  blogSlug: string,
  params?: Record<string, string | string[]>
): string {
  return generateCanonicalUrl({
    path: `/blog/${blogSlug}`,
    params,
    excludeParams: ['comment', 'share']
  })
}

/**
 * Generate canonical URL for search pages
 */
export function generateSearchCanonicalUrl(
  query: string,
  filters?: Record<string, string | string[]>
): string {
  return generateCanonicalUrl({
    path: '/search',
    params: {
      q: query,
      ...filters
    },
    excludeParams: ['page', 'sort']
  })
}

/**
 * Validate canonical URL
 */
export function validateCanonicalUrl(url: string): {
  isValid: boolean
  errors: string[]
  suggestions: string[]
} {
  const errors: string[] = []
  const suggestions: string[] = []
  
  try {
    const parsedUrl = new URL(url)
    
    // Check protocol
    if (parsedUrl.protocol !== 'https:') {
      errors.push('Canonical URL should use HTTPS')
      suggestions.push('Use https:// instead of http://')
    }
    
    // Check for tracking parameters
    const trackingParams = DEFAULT_EXCLUDED_PARAMS.filter(param =>
      parsedUrl.searchParams.has(param)
    )
    if (trackingParams.length > 0) {
      errors.push(`Canonical URL contains tracking parameters: ${trackingParams.join(', ')}`)
      suggestions.push('Remove tracking parameters from canonical URL')
    }
    
    // Check for fragment
    if (parsedUrl.hash) {
      errors.push('Canonical URL should not contain fragment (#)')
      suggestions.push('Remove fragment from canonical URL')
    }
    
    // Check path structure
    if (parsedUrl.pathname.includes('//')) {
      errors.push('Canonical URL contains double slashes in path')
      suggestions.push('Clean up path structure')
    }
    
    // Check for uppercase in path
    if (parsedUrl.pathname !== parsedUrl.pathname.toLowerCase()) {
      suggestions.push('Consider using lowercase paths for consistency')
    }
    
  } catch (error) {
    errors.push('Invalid URL format')
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    suggestions
  }
}

/**
 * Get canonical URL from current page
 */
export function getCurrentCanonicalUrl(): string | null {
  if (typeof window === 'undefined') return null
  
  const canonicalLink = document.querySelector('link[rel="canonical"]') as HTMLLinkElement
  return canonicalLink?.href || null
}

/**
 * Set canonical URL in document head
 */
export function setCanonicalUrl(url: string): void {
  if (typeof window === 'undefined') return

  // Remove existing canonical link
  const existingCanonical = document.querySelector('link[rel="canonical"]')
  if (existingCanonical) {
    existingCanonical.remove()
  }

  // Add new canonical link
  const link = document.createElement('link')
  link.rel = 'canonical'
  link.href = url
  document.head.appendChild(link)
}

/**
 * Generate canonical URL from current window location
 */
export function generateCanonicalFromLocation(
  excludeParams: string[] = DEFAULT_EXCLUDED_PARAMS
): string {
  if (typeof window === 'undefined') {
    return process.env.NEXT_PUBLIC_SITE_URL || 'https://syndicaps.com'
  }

  const url = new URL(window.location.href)

  // Remove excluded parameters
  excludeParams.forEach(param => {
    url.searchParams.delete(param)
  })

  // Clean up the URL
  return generateCanonicalUrl({
    path: url.pathname,
    params: Object.fromEntries(url.searchParams.entries())
  })
}
