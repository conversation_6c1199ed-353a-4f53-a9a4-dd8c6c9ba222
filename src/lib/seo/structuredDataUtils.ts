/**
 * Structured Data Utilities
 * 
 * Helper functions for generating comprehensive structured data
 * for different page types and content.
 * 
 * <AUTHOR> Team
 */

import { 
  OrganizationData, 
  ProductData, 
  ArticleData, 
  BreadcrumbData,
  WebsiteData,
  LocalBusinessData 
} from '@/components/seo/StructuredData'

/**
 * Generate organization structured data for Syndicaps
 */
export function generateSyndicapsOrganization(): OrganizationData {
  return {
    name: 'Syndicaps',
    url: 'https://syndicaps.com',
    logo: 'https://syndicaps.com/logo.png',
    description: 'Premium artisan keycaps and mechanical keyboard community platform',
    contactPoint: {
      telephone: '******-KEYCAPS',
      contactType: 'customer service',
      email: '<EMAIL>'
    },
    sameAs: [
      'https://twitter.com/syndicaps',
      'https://instagram.com/syndicaps',
      'https://discord.gg/syndicaps',
      'https://facebook.com/syndicaps'
    ],
    address: {
      streetAddress: '123 Keycap Street',
      addressLocality: 'Tech City',
      addressRegion: 'CA',
      postalCode: '90210',
      addressCountry: 'US'
    }
  }
}

/**
 * Generate website structured data with search functionality
 */
export function generateWebsiteData(): WebsiteData {
  return {
    name: 'Syndicaps',
    url: 'https://syndicaps.com',
    description: 'Discover premium keycaps, join raffles, and connect with the mechanical keyboard community',
    potentialAction: {
      target: 'https://syndicaps.com/search?q={search_term_string}',
      queryInput: 'required name=search_term_string'
    }
  }
}

/**
 * Generate product structured data from product object
 */
export function generateProductStructuredData(product: any): ProductData {
  return {
    name: product.name,
    description: product.description || `Premium ${product.name} keycap from Syndicaps`,
    image: product.images || [product.image || 'https://syndicaps.com/placeholder-product.jpg'],
    sku: product.sku || product.id,
    brand: 'Syndicaps',
    category: product.category || 'Keycaps',
    offers: {
      price: product.price || 0,
      currency: product.currency || 'USD',
      availability: product.inStock ? 'InStock' : 'OutOfStock',
      validFrom: product.availableFrom,
      validThrough: product.availableUntil
    },
    ...(product.rating && {
      aggregateRating: {
        ratingValue: product.rating.average,
        reviewCount: product.rating.count,
        bestRating: 5,
        worstRating: 1
      }
    }),
    ...(product.reviews && product.reviews.length > 0 && {
      review: product.reviews.slice(0, 5).map((review: any) => ({
        author: review.author?.name || 'Anonymous',
        datePublished: review.createdAt?.toISOString() || new Date().toISOString(),
        reviewBody: review.content,
        reviewRating: {
          ratingValue: review.rating,
          bestRating: 5
        }
      }))
    })
  }
}

/**
 * Generate article structured data from blog post or community content
 */
export function generateArticleStructuredData(article: any): ArticleData {
  return {
    headline: article.title,
    description: article.description || article.excerpt,
    image: article.images || [article.image || 'https://syndicaps.com/placeholder-image.png'],
    author: {
      name: article.author?.name || 'Syndicaps Team',
      url: article.author?.profileUrl
    },
    publisher: {
      name: 'Syndicaps',
      logo: 'https://syndicaps.com/logo.png'
    },
    datePublished: article.publishedAt?.toISOString() || article.createdAt?.toISOString(),
    dateModified: article.updatedAt?.toISOString(),
    articleSection: article.category || 'Community',
    wordCount: article.wordCount,
    readingTime: article.readingTime,
    keywords: article.tags || []
  }
}

/**
 * Generate breadcrumb data from URL path
 */
export function generateBreadcrumbFromPath(pathname: string): BreadcrumbData {
  const baseUrl = 'https://syndicaps.com'
  const segments = pathname.split('/').filter(Boolean)
  
  const items = [
    { name: 'Home', url: baseUrl }
  ]
  
  let currentPath = ''
  
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`
    
    // Convert segment to readable name
    let name = segment
      .replace(/-/g, ' ')
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
    
    // Special cases for known routes
    const routeNames: Record<string, string> = {
      'shop': 'Shop',
      'products': 'Products',
      'community': 'Community',
      'blog': 'Blog',
      'profile': 'Profile',
      'admin': 'Admin',
      'about': 'About',
      'contact': 'Contact',
      'faq': 'FAQ',
      'support': 'Support',
      'terms-of-service': 'Terms of Service',
      'privacy-policy': 'Privacy Policy',
      'shipping-returns': 'Shipping & Returns',
      'discussions': 'Discussions',
      'challenges': 'Challenges',
      'submissions': 'Submissions',
      'rankings': 'Rankings',
      'activity': 'Activity'
    }
    
    if (routeNames[segment]) {
      name = routeNames[segment]
    }
    
    items.push({
      name,
      url: `${baseUrl}${currentPath}`
    })
  })
  
  return { items }
}

/**
 * Generate local business structured data for Syndicaps
 */
export function generateLocalBusinessData(): LocalBusinessData {
  const organization = generateSyndicapsOrganization()
  
  return {
    ...organization,
    priceRange: '$10-$500',
    openingHours: [
      'Mo-Fr 09:00-17:00',
      'Sa 10:00-16:00'
    ],
    geo: {
      latitude: 34.0522,
      longitude: -118.2437
    },
    aggregateRating: {
      ratingValue: 4.8,
      reviewCount: 1250
    }
  }
}

/**
 * Generate FAQ structured data
 */
export function generateFAQStructuredData(faqs: Array<{ question: string; answer: string }>) {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  }
}

/**
 * Generate review structured data
 */
export function generateReviewStructuredData(reviews: any[]) {
  return reviews.map(review => ({
    '@context': 'https://schema.org',
    '@type': 'Review',
    itemReviewed: {
      '@type': 'Product',
      name: review.productName
    },
    author: {
      '@type': 'Person',
      name: review.author?.name || 'Anonymous'
    },
    reviewRating: {
      '@type': 'Rating',
      ratingValue: review.rating,
      bestRating: 5,
      worstRating: 1
    },
    reviewBody: review.content,
    datePublished: review.createdAt?.toISOString()
  }))
}

/**
 * Generate event structured data for community events
 */
export function generateEventStructuredData(event: any) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Event',
    name: event.title,
    description: event.description,
    startDate: event.startDate?.toISOString(),
    endDate: event.endDate?.toISOString(),
    location: {
      '@type': 'VirtualLocation',
      url: event.url || 'https://syndicaps.com/community'
    },
    organizer: {
      '@type': 'Organization',
      name: 'Syndicaps',
      url: 'https://syndicaps.com'
    },
    eventStatus: 'https://schema.org/EventScheduled',
    eventAttendanceMode: 'https://schema.org/OnlineEventAttendanceMode',
    isAccessibleForFree: true
  }
}

/**
 * Generate course structured data for tutorials
 */
export function generateCourseStructuredData(course: any) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Course',
    name: course.title,
    description: course.description,
    provider: {
      '@type': 'Organization',
      name: 'Syndicaps',
      url: 'https://syndicaps.com'
    },
    courseCode: course.id,
    educationalLevel: 'Beginner',
    teaches: course.skills || [],
    timeRequired: course.duration ? `PT${course.duration}M` : undefined,
    isAccessibleForFree: true
  }
}
