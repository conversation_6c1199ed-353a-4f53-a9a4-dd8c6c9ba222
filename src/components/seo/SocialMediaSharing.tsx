/**
 * Social Media Sharing Component
 * 
 * Comprehensive social media sharing component with optimized sharing URLs,
 * analytics tracking, and customizable sharing options for different platforms.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Share2,
  Twitter,
  Facebook,
  Linkedin,
  Instagram,
  MessageCircle,
  Mail,
  Link,
  Copy,
  Check,
  Download
} from 'lucide-react'
import { toast } from 'react-hot-toast'

/**
 * Social sharing interfaces
 */
export interface SocialShareData {
  url: string
  title: string
  description: string
  image?: string
  hashtags?: string[]
  via?: string
  related?: string[]
  // Enhanced social sharing data
  author?: string
  publishedTime?: string
  modifiedTime?: string
  section?: string
  tags?: string[]
  locale?: string
  siteName?: string
  type?: 'article' | 'product' | 'website' | 'profile'
  // Platform-specific data
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player'
  twitterSite?: string
  twitterCreator?: string
  facebookAppId?: string
  // Additional metadata
  price?: {
    amount: number
    currency: string
  }
  availability?: string
  rating?: {
    value: number
    scale: number
    count: number
  }
}

export interface SocialPlatform {
  name: string
  icon: React.ComponentType<any>
  color: string
  shareUrl: (data: SocialShareData) => string
  analytics?: string
}

export interface SocialMediaSharingProps {
  data: SocialShareData
  platforms?: string[]
  showLabels?: boolean
  size?: 'small' | 'medium' | 'large'
  variant?: 'horizontal' | 'vertical' | 'grid' | 'dropdown'
  trackAnalytics?: boolean
  customPlatforms?: SocialPlatform[]
}

/**
 * Social Media Sharing Component
 */
export const SocialMediaSharing: React.FC<SocialMediaSharingProps> = ({
  data,
  platforms = ['twitter', 'facebook', 'linkedin', 'discord', 'email', 'copy'],
  showLabels = false,
  size = 'medium',
  variant = 'horizontal',
  trackAnalytics = true,
  customPlatforms = []
}) => {
  const [copiedUrl, setCopiedUrl] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  // Default social platforms
  const defaultPlatforms: Record<string, SocialPlatform> = {
    twitter: {
      name: 'Twitter',
      icon: Twitter,
      color: '#1DA1F2',
      shareUrl: (data) => {
        const params = new URLSearchParams({
          url: data.url,
          text: data.title,
          ...(data.hashtags && { hashtags: data.hashtags.join(',') }),
          ...(data.via && { via: data.via })
        })
        return `https://twitter.com/intent/tweet?${params.toString()}`
      },
      analytics: 'twitter_share'
    },
    facebook: {
      name: 'Facebook',
      icon: Facebook,
      color: '#4267B2',
      shareUrl: (data) => {
        const params = new URLSearchParams({
          u: data.url,
          quote: data.description
        })
        return `https://www.facebook.com/sharer/sharer.php?${params.toString()}`
      },
      analytics: 'facebook_share'
    },
    linkedin: {
      name: 'LinkedIn',
      icon: Linkedin,
      color: '#0077B5',
      shareUrl: (data) => {
        const params = new URLSearchParams({
          url: data.url,
          title: data.title,
          summary: data.description
        })
        return `https://www.linkedin.com/sharing/share-offsite/?${params.toString()}`
      },
      analytics: 'linkedin_share'
    },
    discord: {
      name: 'Discord',
      icon: MessageCircle,
      color: '#5865F2',
      shareUrl: (data) => {
        // Discord doesn't have a direct share URL, so we copy a formatted message
        return `discord://share?text=${encodeURIComponent(`${data.title}\n${data.description}\n${data.url}`)}`
      },
      analytics: 'discord_share'
    },
    instagram: {
      name: 'Instagram',
      icon: Instagram,
      color: '#E4405F',
      shareUrl: (data) => {
        // Instagram doesn't support direct URL sharing, so we open the app
        return `instagram://share?text=${encodeURIComponent(data.title)}`
      },
      analytics: 'instagram_share'
    },
    email: {
      name: 'Email',
      icon: Mail,
      color: '#34495E',
      shareUrl: (data) => {
        const params = new URLSearchParams({
          subject: data.title,
          body: `${data.description}\n\n${data.url}`
        })
        return `mailto:?${params.toString()}`
      },
      analytics: 'email_share'
    },
    copy: {
      name: 'Copy Link',
      icon: copiedUrl ? Check : Copy,
      color: '#6366F1',
      shareUrl: (data) => data.url,
      analytics: 'copy_link'
    },
    reddit: {
      name: 'Reddit',
      icon: MessageCircle,
      color: '#FF4500',
      shareUrl: (data) => {
        const params = new URLSearchParams({
          url: data.url,
          title: data.title
        })
        return `https://www.reddit.com/submit?${params.toString()}`
      },
      analytics: 'reddit_share'
    },
    pinterest: {
      name: 'Pinterest',
      icon: Download,
      color: '#BD081C',
      shareUrl: (data) => {
        const params = new URLSearchParams({
          url: data.url,
          description: data.title,
          ...(data.image && { media: data.image })
        })
        return `https://pinterest.com/pin/create/button/?${params.toString()}`
      },
      analytics: 'pinterest_share'
    },
    whatsapp: {
      name: 'WhatsApp',
      icon: MessageCircle,
      color: '#25D366',
      shareUrl: (data) => {
        const text = `${data.title}\n${data.description}\n${data.url}`
        return `https://wa.me/?text=${encodeURIComponent(text)}`
      },
      analytics: 'whatsapp_share'
    },
    telegram: {
      name: 'Telegram',
      icon: MessageCircle,
      color: '#0088CC',
      shareUrl: (data) => {
        const params = new URLSearchParams({
          url: data.url,
          text: `${data.title}\n${data.description}`
        })
        return `https://t.me/share/url?${params.toString()}`
      },
      analytics: 'telegram_share'
    }
  }

  // Merge custom platforms with defaults
  const allPlatforms = { ...defaultPlatforms, ...customPlatforms.reduce((acc, platform) => {
    acc[platform.name.toLowerCase()] = platform
    return acc
  }, {} as Record<string, SocialPlatform>) }

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'small': return 'w-8 h-8 text-sm'
      case 'large': return 'w-12 h-12 text-lg'
      default: return 'w-10 h-10 text-base'
    }
  }

  // Get variant classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'vertical': return 'flex flex-col space-y-2'
      case 'grid': return 'grid grid-cols-3 gap-2'
      case 'dropdown': return 'relative'
      default: return 'flex flex-wrap gap-2'
    }
  }

  // Handle platform share
  const handleShare = async (platformKey: string) => {
    const platform = allPlatforms[platformKey]
    if (!platform) return

    // Track analytics
    if (trackAnalytics && platform.analytics) {
      trackSocialShare(platform.analytics, data.url)
    }

    // Handle copy link specially
    if (platformKey === 'copy') {
      try {
        await navigator.clipboard.writeText(data.url)
        setCopiedUrl(true)
        toast.success('Link copied to clipboard!')
        setTimeout(() => setCopiedUrl(false), 2000)
      } catch (error) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea')
        textArea.value = data.url
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        setCopiedUrl(true)
        toast.success('Link copied to clipboard!')
        setTimeout(() => setCopiedUrl(false), 2000)
      }
      return
    }

    // Handle Discord specially (copy formatted message)
    if (platformKey === 'discord') {
      const discordMessage = `**${data.title}**\n${data.description}\n${data.url}`
      try {
        await navigator.clipboard.writeText(discordMessage)
        toast.success('Discord message copied! Paste it in your server.')
      } catch (error) {
        toast.error('Failed to copy Discord message')
      }
      return
    }

    // Open share URL
    const shareUrl = platform.shareUrl(data)
    window.open(shareUrl, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes')
  }

  // Track social share analytics
  const trackSocialShare = (platform: string, url: string) => {
    // Integration with analytics services
    if (typeof window !== 'undefined') {
      // Google Analytics 4
      if (window.gtag) {
        window.gtag('event', 'share', {
          method: platform,
          content_type: 'url',
          item_id: url
        })
      }

      // Facebook Pixel
      if (window.fbq) {
        window.fbq('track', 'Share', {
          content_type: 'url',
          content_id: url
        })
      }

      // Custom analytics
      if (window.analytics) {
        window.analytics.track('Social Share', {
          platform,
          url,
          title: data.title
        })
      }
    }
  }

  // Render share button
  const renderShareButton = (platformKey: string, platform: SocialPlatform) => {
    const Icon = platform.icon
    const sizeClasses = getSizeClasses()
    
    return (
      <motion.button
        key={platformKey}
        onClick={() => handleShare(platformKey)}
        className={`
          ${sizeClasses} 
          flex items-center justify-center rounded-lg
          bg-gray-800 hover:bg-gray-700 border border-gray-600
          text-white transition-all duration-200
          hover:scale-105 hover:shadow-lg
          focus:outline-none focus:ring-2 focus:ring-purple-500
        `}
        style={{
          '--hover-color': platform.color
        } as React.CSSProperties}
        whileHover={{ 
          backgroundColor: platform.color,
          borderColor: platform.color
        }}
        whileTap={{ scale: 0.95 }}
        title={`Share on ${platform.name}`}
        aria-label={`Share on ${platform.name}`}
      >
        <Icon className="w-5 h-5" />
        {showLabels && (
          <span className="ml-2 text-sm font-medium">
            {platform.name}
          </span>
        )}
      </motion.button>
    )
  }

  // Render dropdown variant
  if (variant === 'dropdown') {
    return (
      <div className="relative">
        <motion.button
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className={`
            ${getSizeClasses()}
            flex items-center justify-center rounded-lg
            bg-purple-600 hover:bg-purple-700 text-white
            transition-colors duration-200
            focus:outline-none focus:ring-2 focus:ring-purple-500
          `}
          whileTap={{ scale: 0.95 }}
          aria-label="Share options"
        >
          <Share2 className="w-5 h-5" />
        </motion.button>

        {isDropdownOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-10"
              onClick={() => setIsDropdownOpen(false)}
            />
            
            {/* Dropdown menu */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-600 rounded-lg shadow-xl z-20"
            >
              <div className="p-2 space-y-1">
                {platforms.map(platformKey => {
                  const platform = allPlatforms[platformKey]
                  if (!platform) return null

                  const Icon = platform.icon
                  return (
                    <button
                      key={platformKey}
                      onClick={() => {
                        handleShare(platformKey)
                        setIsDropdownOpen(false)
                      }}
                      className="w-full flex items-center space-x-3 px-3 py-2 text-left text-white hover:bg-gray-700 rounded-md transition-colors"
                    >
                      <Icon className="w-4 h-4" style={{ color: platform.color }} />
                      <span className="text-sm">{platform.name}</span>
                    </button>
                  )
                })}
              </div>
            </motion.div>
          </>
        )}
      </div>
    )
  }

  // Render standard variants
  return (
    <div className={getVariantClasses()}>
      {platforms.map(platformKey => {
        const platform = allPlatforms[platformKey]
        if (!platform) return null
        return renderShareButton(platformKey, platform)
      })}
    </div>
  )
}

/**
 * Quick Share Button Component
 */
export const QuickShareButton: React.FC<{
  data: SocialShareData
  className?: string
}> = ({ data, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className={`relative ${className}`}>
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
        whileTap={{ scale: 0.95 }}
      >
        <Share2 className="w-4 h-4" />
        <span>Share</span>
      </motion.button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute top-full right-0 mt-2 z-20">
            <SocialMediaSharing
              data={data}
              variant="vertical"
              showLabels={true}
              size="medium"
            />
          </div>
        </>
      )}
    </div>
  )
}

/**
 * Social Share Analytics Hook
 */
export const useSocialShareAnalytics = () => {
  const trackShare = (platform: string, url: string, title: string) => {
    if (typeof window !== 'undefined') {
      // Track with multiple analytics services
      const eventData = {
        event: 'social_share',
        platform,
        url,
        title,
        timestamp: new Date().toISOString()
      }

      // Google Analytics
      if (window.gtag) {
        window.gtag('event', 'share', {
          method: platform,
          content_type: 'url',
          item_id: url
        })
      }

      // Custom analytics endpoint
      fetch('/api/analytics/social-share', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(eventData)
      }).catch(console.error)
    }
  }

  return { trackShare }
}

// Extend window type for analytics
declare global {
  interface Window {
    gtag?: (...args: any[]) => void
    fbq?: (...args: any[]) => void
    analytics?: {
      track: (event: string, properties: any) => void
    }
  }
}

export default SocialMediaSharing
