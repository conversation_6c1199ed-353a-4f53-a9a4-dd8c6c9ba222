/**
 * Enhanced Structured Data Component
 * 
 * Comprehensive JSON-LD structured data implementation for better
 * search engine understanding and rich snippets.
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import Script from 'next/script'

/**
 * Structured data interfaces
 */
export interface OrganizationData {
  name: string
  url: string
  logo: string
  description: string
  contactPoint?: {
    telephone: string
    contactType: string
    email: string
  }
  sameAs?: string[]
  address?: {
    streetAddress: string
    addressLocality: string
    addressRegion: string
    postalCode: string
    addressCountry: string
  }
}

export interface ProductData {
  name: string
  description: string
  image: string[]
  sku: string
  brand: string
  category: string
  offers: {
    price: number
    currency: string
    availability: string
    validFrom?: string
    validThrough?: string
  }
  aggregateRating?: {
    ratingValue: number
    reviewCount: number
    bestRating?: number
    worstRating?: number
  }
  review?: Array<{
    author: string
    datePublished: string
    reviewBody: string
    reviewRating: {
      ratingValue: number
      bestRating?: number
    }
  }>
}

export interface ArticleData {
  headline: string
  description: string
  image: string[]
  author: {
    name: string
    url?: string
  }
  publisher: {
    name: string
    logo: string
  }
  datePublished: string
  dateModified?: string
  articleSection: string
  wordCount?: number
  readingTime?: number
  keywords?: string[]
}

export interface BreadcrumbData {
  items: Array<{
    name: string
    url: string
  }>
}

export interface WebsiteData {
  name: string
  url: string
  description: string
  potentialAction?: {
    target: string
    queryInput: string
  }
}

export interface LocalBusinessData extends OrganizationData {
  priceRange: string
  openingHours?: string[]
  geo?: {
    latitude: number
    longitude: number
  }
  aggregateRating?: {
    ratingValue: number
    reviewCount: number
  }
}

/**
 * Component props
 */
interface StructuredDataProps {
  organization?: OrganizationData
  product?: ProductData
  article?: ArticleData
  breadcrumb?: BreadcrumbData
  website?: WebsiteData
  localBusiness?: LocalBusinessData
  customSchema?: object[]
}

/**
 * Enhanced Structured Data Component
 */
export const StructuredData: React.FC<StructuredDataProps> = ({
  organization,
  product,
  article,
  breadcrumb,
  website,
  localBusiness,
  customSchema = []
}) => {
  const schemas: object[] = []

  // Organization schema
  if (organization) {
    schemas.push({
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: organization.name,
      url: organization.url,
      logo: {
        '@type': 'ImageObject',
        url: organization.logo
      },
      description: organization.description,
      ...(organization.contactPoint && {
        contactPoint: {
          '@type': 'ContactPoint',
          telephone: organization.contactPoint.telephone,
          contactType: organization.contactPoint.contactType,
          email: organization.contactPoint.email
        }
      }),
      ...(organization.sameAs && { sameAs: organization.sameAs }),
      ...(organization.address && {
        address: {
          '@type': 'PostalAddress',
          streetAddress: organization.address.streetAddress,
          addressLocality: organization.address.addressLocality,
          addressRegion: organization.address.addressRegion,
          postalCode: organization.address.postalCode,
          addressCountry: organization.address.addressCountry
        }
      })
    })
  }

  // Product schema
  if (product) {
    schemas.push({
      '@context': 'https://schema.org',
      '@type': 'Product',
      name: product.name,
      description: product.description,
      image: product.image,
      sku: product.sku,
      brand: {
        '@type': 'Brand',
        name: product.brand
      },
      category: product.category,
      offers: {
        '@type': 'Offer',
        price: product.offers.price,
        priceCurrency: product.offers.currency,
        availability: `https://schema.org/${product.offers.availability}`,
        ...(product.offers.validFrom && { validFrom: product.offers.validFrom }),
        ...(product.offers.validThrough && { validThrough: product.offers.validThrough })
      },
      ...(product.aggregateRating && {
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: product.aggregateRating.ratingValue,
          reviewCount: product.aggregateRating.reviewCount,
          bestRating: product.aggregateRating.bestRating || 5,
          worstRating: product.aggregateRating.worstRating || 1
        }
      }),
      ...(product.review && {
        review: product.review.map(review => ({
          '@type': 'Review',
          author: {
            '@type': 'Person',
            name: review.author
          },
          datePublished: review.datePublished,
          reviewBody: review.reviewBody,
          reviewRating: {
            '@type': 'Rating',
            ratingValue: review.reviewRating.ratingValue,
            bestRating: review.reviewRating.bestRating || 5
          }
        }))
      })
    })
  }

  // Article schema
  if (article) {
    schemas.push({
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: article.headline,
      description: article.description,
      image: article.image,
      author: {
        '@type': 'Person',
        name: article.author.name,
        ...(article.author.url && { url: article.author.url })
      },
      publisher: {
        '@type': 'Organization',
        name: article.publisher.name,
        logo: {
          '@type': 'ImageObject',
          url: article.publisher.logo
        }
      },
      datePublished: article.datePublished,
      ...(article.dateModified && { dateModified: article.dateModified }),
      articleSection: article.articleSection,
      ...(article.wordCount && { wordCount: article.wordCount }),
      ...(article.readingTime && { 
        timeRequired: `PT${article.readingTime}M` 
      }),
      ...(article.keywords && { keywords: article.keywords.join(', ') })
    })
  }

  // Breadcrumb schema
  if (breadcrumb) {
    schemas.push({
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumb.items.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: item.name,
        item: item.url
      }))
    })
  }

  // Website schema
  if (website) {
    schemas.push({
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: website.name,
      url: website.url,
      description: website.description,
      ...(website.potentialAction && {
        potentialAction: {
          '@type': 'SearchAction',
          target: website.potentialAction.target,
          'query-input': website.potentialAction.queryInput
        }
      })
    })
  }

  // Local Business schema
  if (localBusiness) {
    schemas.push({
      '@context': 'https://schema.org',
      '@type': 'LocalBusiness',
      name: localBusiness.name,
      url: localBusiness.url,
      logo: {
        '@type': 'ImageObject',
        url: localBusiness.logo
      },
      description: localBusiness.description,
      priceRange: localBusiness.priceRange,
      ...(localBusiness.openingHours && { openingHours: localBusiness.openingHours }),
      ...(localBusiness.geo && {
        geo: {
          '@type': 'GeoCoordinates',
          latitude: localBusiness.geo.latitude,
          longitude: localBusiness.geo.longitude
        }
      }),
      ...(localBusiness.aggregateRating && {
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: localBusiness.aggregateRating.ratingValue,
          reviewCount: localBusiness.aggregateRating.reviewCount
        }
      }),
      ...(localBusiness.contactPoint && {
        contactPoint: {
          '@type': 'ContactPoint',
          telephone: localBusiness.contactPoint.telephone,
          contactType: localBusiness.contactPoint.contactType,
          email: localBusiness.contactPoint.email
        }
      }),
      ...(localBusiness.address && {
        address: {
          '@type': 'PostalAddress',
          streetAddress: localBusiness.address.streetAddress,
          addressLocality: localBusiness.address.addressLocality,
          addressRegion: localBusiness.address.addressRegion,
          postalCode: localBusiness.address.postalCode,
          addressCountry: localBusiness.address.addressCountry
        }
      })
    })
  }

  // Add custom schemas
  schemas.push(...customSchema)

  if (schemas.length === 0) {
    return null
  }

  return (
    <>
      {schemas.map((schema, index) => (
        <Script
          key={index}
          id={`structured-data-${index}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema, null, 0)
          }}
        />
      ))}
    </>
  )
}

/**
 * Breadcrumb Component with Structured Data
 */
interface BreadcrumbComponentProps {
  items: Array<{
    name: string
    url: string
    isCurrentPage?: boolean
  }>
  className?: string
}

export const BreadcrumbComponent: React.FC<BreadcrumbComponentProps> = ({
  items,
  className = ''
}) => {
  return (
    <>
      {/* Structured Data */}
      <StructuredData
        breadcrumb={{
          items: items.map(item => ({
            name: item.name,
            url: item.url
          }))
        }}
      />

      {/* Visual Breadcrumb */}
      <nav
        aria-label="Breadcrumb"
        className={`flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400 ${className}`}
      >
        <ol className="flex items-center space-x-2">
          {items.map((item, index) => (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <span className="mx-2 text-gray-400">/</span>
              )}
              {item.isCurrentPage ? (
                <span
                  className="text-gray-900 dark:text-gray-100 font-medium"
                  aria-current="page"
                >
                  {item.name}
                </span>
              ) : (
                <a
                  href={item.url}
                  className="text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 transition-colors"
                >
                  {item.name}
                </a>
              )}
            </li>
          ))}
        </ol>
      </nav>
    </>
  )
}

export default StructuredData
