/**
 * Enhanced Meta Tags Component
 * 
 * Comprehensive meta tags implementation for improved SEO,
 * social media sharing, and search engine optimization.
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import Head from 'next/head'
import { SocialShareData } from './SocialMediaSharing'

interface EnhancedMetaTagsProps {
  // Basic SEO
  title: string
  description: string
  keywords?: string[]
  canonical?: string
  noIndex?: boolean
  noFollow?: boolean
  
  // Social sharing data
  socialData?: SocialShareData
  
  // Additional meta tags
  author?: string
  robots?: string
  viewport?: string
  themeColor?: string
  
  // Language and localization
  language?: string
  alternateLanguages?: Array<{
    hreflang: string
    href: string
  }>
  
  // App-specific
  appleTouchIcon?: string
  favicon?: string
  manifest?: string
  
  // Custom meta tags
  customMeta?: Array<{
    name?: string
    property?: string
    content: string
  }>
}

export const EnhancedMetaTags: React.FC<EnhancedMetaTagsProps> = ({
  title,
  description,
  keywords = [],
  canonical,
  noIndex = false,
  noFollow = false,
  socialData,
  author,
  robots,
  viewport = 'width=device-width, initial-scale=1, viewport-fit=cover',
  themeColor = '#6366f1',
  language = 'en',
  alternateLanguages = [],
  appleTouchIcon = '/apple-touch-icon.png',
  favicon = '/favicon.ico',
  manifest = '/manifest.json',
  customMeta = []
}) => {
  // Generate robots directive
  const robotsDirective = robots || [
    !noIndex ? 'index' : 'noindex',
    !noFollow ? 'follow' : 'nofollow',
    'max-snippet:-1',
    'max-image-preview:large',
    'max-video-preview:-1'
  ].join(', ')

  // Default social data
  const defaultSocialData: Partial<SocialShareData> = {
    siteName: 'Syndicaps',
    type: 'website',
    twitterCard: 'summary_large_image',
    twitterSite: '@syndicaps',
    locale: 'en_US',
    ...socialData
  }

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      {keywords.length > 0 && (
        <meta name="keywords" content={keywords.join(', ')} />
      )}
      {author && <meta name="author" content={author} />}
      <meta name="robots" content={robotsDirective} />
      <meta name="viewport" content={viewport} />
      <meta name="theme-color" content={themeColor} />
      <meta httpEquiv="Content-Language" content={language} />
      
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Language Alternatives */}
      {alternateLanguages.map((alt, index) => (
        <link
          key={index}
          rel="alternate"
          hrefLang={alt.hreflang}
          href={alt.href}
        />
      ))}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={defaultSocialData.title || title} />
      <meta property="og:description" content={defaultSocialData.description || description} />
      <meta property="og:url" content={defaultSocialData.url || canonical || ''} />
      <meta property="og:site_name" content={defaultSocialData.siteName} />
      <meta property="og:type" content={defaultSocialData.type} />
      <meta property="og:locale" content={defaultSocialData.locale} />
      
      {defaultSocialData.image && (
        <>
          <meta property="og:image" content={defaultSocialData.image} />
          <meta property="og:image:alt" content={defaultSocialData.title || title} />
          <meta property="og:image:width" content="1200" />
          <meta property="og:image:height" content="630" />
        </>
      )}
      
      {defaultSocialData.author && (
        <meta property="og:author" content={defaultSocialData.author} />
      )}
      
      {defaultSocialData.publishedTime && (
        <meta property="article:published_time" content={defaultSocialData.publishedTime} />
      )}
      
      {defaultSocialData.modifiedTime && (
        <meta property="article:modified_time" content={defaultSocialData.modifiedTime} />
      )}
      
      {defaultSocialData.section && (
        <meta property="article:section" content={defaultSocialData.section} />
      )}
      
      {defaultSocialData.tags && defaultSocialData.tags.length > 0 && (
        defaultSocialData.tags.map((tag, index) => (
          <meta key={index} property="article:tag" content={tag} />
        ))
      )}
      
      {/* Product-specific Open Graph */}
      {defaultSocialData.type === 'product' && defaultSocialData.price && (
        <>
          <meta property="product:price:amount" content={defaultSocialData.price.amount.toString()} />
          <meta property="product:price:currency" content={defaultSocialData.price.currency} />
        </>
      )}
      
      {defaultSocialData.availability && (
        <meta property="product:availability" content={defaultSocialData.availability} />
      )}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={defaultSocialData.twitterCard} />
      <meta name="twitter:site" content={defaultSocialData.twitterSite} />
      {defaultSocialData.twitterCreator && (
        <meta name="twitter:creator" content={defaultSocialData.twitterCreator} />
      )}
      <meta name="twitter:title" content={defaultSocialData.title || title} />
      <meta name="twitter:description" content={defaultSocialData.description || description} />
      
      {defaultSocialData.image && (
        <>
          <meta name="twitter:image" content={defaultSocialData.image} />
          <meta name="twitter:image:alt" content={defaultSocialData.title || title} />
        </>
      )}
      
      {/* Facebook App ID */}
      {defaultSocialData.facebookAppId && (
        <meta property="fb:app_id" content={defaultSocialData.facebookAppId} />
      )}
      
      {/* App Icons and Manifest */}
      <link rel="icon" href={favicon} />
      <link rel="apple-touch-icon" href={appleTouchIcon} />
      <link rel="manifest" href={manifest} />
      
      {/* Apple-specific Meta Tags */}
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="Syndicaps" />
      
      {/* Microsoft-specific Meta Tags */}
      <meta name="msapplication-TileColor" content={themeColor} />
      <meta name="msapplication-config" content="/browserconfig.xml" />
      
      {/* Additional Security and Performance */}
      <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="mobile-web-app-capable" content="yes" />
      
      {/* DNS Prefetch for Performance */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      
      {/* Preconnect for Critical Resources */}
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* Custom Meta Tags */}
      {customMeta.map((meta, index) => (
        <meta
          key={index}
          {...(meta.name && { name: meta.name })}
          {...(meta.property && { property: meta.property })}
          content={meta.content}
        />
      ))}
      
      {/* Rating and Review Meta Tags */}
      {defaultSocialData.rating && (
        <>
          <meta name="rating" content={defaultSocialData.rating.value.toString()} />
          <meta name="review_count" content={defaultSocialData.rating.count.toString()} />
        </>
      )}
      
      {/* Geographic Meta Tags */}
      <meta name="geo.region" content="US-CA" />
      <meta name="geo.placename" content="Tech City" />
      <meta name="geo.position" content="34.0522;-118.2437" />
      <meta name="ICBM" content="34.0522, -118.2437" />
      
      {/* Copyright and Legal */}
      <meta name="copyright" content="© 2025 Syndicaps. All rights reserved." />
      <meta name="distribution" content="global" />
      <meta name="rating" content="general" />
      
      {/* Cache Control */}
      <meta httpEquiv="Cache-Control" content="public, max-age=31536000" />
    </Head>
  )
}

export default EnhancedMetaTags
